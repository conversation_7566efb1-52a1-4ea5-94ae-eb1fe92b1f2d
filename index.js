const { enableVaultConfig } = require('vault-helper');

const startServer = () => {
    // Setup global helper for requireSrc (require from src folder)
    if (!global.requireSrc) {
      global.requireSrc = (name) => {
        return require(`${__dirname}/src/${name}`)
      }
    }
  
    const { Server, Router } = requireSrc('commons');
  
    // add all controllers in /src/routes
    Router.addControllers(require('./src/routes/v1/portal'));
  
    Server.applyRoutes(Router).startServer().logRoutes();
  
    module.exports = Server; // for mocha testing
  }

const main = async () => {
    try {
      const listENV = ['dev', 'sit', 'uat', 'pt', 'prod'];
      if (listENV.includes(process.env.NODE_ENV)) {
        await enableVaultConfig('AP1970-PartnerEcosystem', ['DB_USER_PORTAL', 'DB_PASSWORD_PORTAL', 'CA_PORTAL_KEY', 'CA_PORTAL_SECRET'], true);
      }
      startServer();
    } catch (error) {
      console.log('ERROR WHEN TRY TO START SERVER OR ENABLE VAULT CONFIG: ', error);
    }
};

main();

