module.exports = {
  success: {
    http_code: 200,
    biz_code: 1000,
    description: 'Success',
  },
  missing_param: {
    http_code: 400,
    biz_code: 1999,
    description: 'Missing required parameters',
  },
  invalid_param: {
    http_code: 400,
    biz_code: 1999,
    description: 'Invalid parameters entered',
  },
  not_found: {
    http_code: 404,
    biz_code: 1104,
    description: 'Requested entity record does not exist',
  },
  incorrect_creditcard_no: {
    http_code: 400,
    biz_code: 5111,
    description: 'Incorrect creditcard number',
  },
  incorrect_creditcard_status: {
    http_code: 400,
    biz_code: 5112,
    description: 'Incorrect creditcard status',
  },
  invalid_transaction_id: {
    http_code: 404,
    biz_code: 2104,
    description: 'transactionId not found',
  },
  invalid_trans_token: {
    http_code: 400,
    biz_code: 5101,
    description: 'Invalid or expired transactionToken',
  },
  invalid_payment_status: {
    http_code: 400,
    biz_code: 2109,
    description: 'Transaction status is not valid for payment',
  },
  creditcard_not_found: {
    http_code: 404,
    biz_code: 5114,
    description: 'Credit card not found',
  },
  missing_param: {
    http_code: 400,
    biz_code: 1101,
    description: 'Missing required parameters',
  },
  invalid_param: {
    http_code: 400,
    biz_code: 1102,
    description: 'Invalid parameters entered',
  },
  invalid_downstream_response: {
    http_code: 400,
    biz_code: 8101,
    description: 'Invalid response from downstream service',
  },
  generic_catch_all: {
    http_code: 500,
    biz_code: 8101,
    description: 'Invalid response from downstream service',
  },
  request_timeout: {
    http_code: 504,
    biz_code: 9900,
    description: 'API Request Timeout',
  },
  generic_server_error: {
    http_code: 500,
    biz_code: 9700,
    description: 'Generic server side error'
  },
  invalid_application: {
    http_code: 404,
    biz_code: 6130,
    description: 'Invalid application'
  }
};
