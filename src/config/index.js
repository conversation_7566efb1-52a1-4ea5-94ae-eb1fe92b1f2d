const { getConfigObject } = require('microservice-config/envparser');
const deepmerge = require('deepmerge');
const dayjs = require('dayjs');
const globals = {
    apiServiceName: 'portal-applications',
    apiBasePath: 'portal',
    apiVersion: 'v1',
    port: process.env.PORT || 5000,
    logLevel: process.env.LOG_LEVEL || 'info',
    timestamp: {
        formatString: process.env.DATE_FORMAT || 'YYYY-MM-DDTHH:mm:ssZ',
        gmt_local: process.env.DATE_GMT_LOCAL || 'TRUE'
    },
    swaggerOptions: {
        "swaggerDefinition": {
          "info": {
            "title": "Partner Portal APIs",
            "description": "Siam Commercial Bank Partner Ecosystem API",
            "version": "1.0.0"
          },
          "host": "elb1s-internet-elb-**********.ap-southeast-1.elb.amazonaws.com",
          "basePath": "/partners/v1",
          "schemes": ["https"]
        },
        "apis": ["./src/routes/v1/portal/*/*.controller.js"]
    },
    cacheTTL: process.env.CACHE_TTL || 86400, // 1 day default cache TTL
};

globals.timestamp.format = () => {
    return (globals.timestamp.gmt_local === 'TRUE')?
                    dayjs().format(globals.timestampformatString)
            // timestamp.gmt_local === 'FALSE'
                  : dayjs().utc().format(globals.timestampformatString);
}

const configFromFile = require(`./config.${process.env.NODE_ENV || 'local'}.json`);

// merges configFromFile with globals giving configFromFile a higher priority
const staticConfig = deepmerge(globals, configFromFile)

// reads the deployment yml file for env variables and convert them into a config object
const deploymentConfig = getConfigObject();

// merges deploymentConfig with staticConfig giving deploymentConfig a higher priority
const config = deepmerge(staticConfig, deploymentConfig);

module.exports = config;
