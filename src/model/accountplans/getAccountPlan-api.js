const logger = requireSrc('log')
const axios = require('axios');
const https = require('https');


const { portalApiBasePath, portalTenantId, portalApiPort } = requireSrc('config');
const Cache = requireSrc('util').Cache;
const accountPlanCache = new Cache();

module.exports = {
  // Retrieving account plans
  getAccountPlanByName: async (payload) => {
    logger.info(`getAccountPlanByName()`);
    const cacheId = 'AccountPlanLookup';
    const caLookup = () => {
      return new Promise(async(resolve, reject) => {
        
        const httpsAgent = new https.Agent({
          rejectUnauthorized: false
        })
  
        const axiosHeaders = {
          Authorization: payload.oauthToken
        }
  
        try
        {
          const result = await axios.get(`${portalApiBasePath}:${portalApiPort}/${portalTenantId}/AccountPlans`,
          {
            headers: axiosHeaders,
            httpsAgent: httpsAgent,
            params: {
              $filter: `Name eq '${payload.accountPlanName}'`
            }
          })
          const str = result.config.url
          const queryString = str?.slice(str.indexOf('?') + 1);
    
          const response = {
            statusCode: result.status,
            body: result.data,
            request:  
            {
              uri: {
                  protocol: result.request.protocol,
                  host: result.request.host,
                  port: result.request.agent.defaultPort,
                  hostname: result.request.agent.host,
                  auth: result.config.auth,
                  search: queryString !== result.config.url ? `?${queryString}`: null,
                  query: queryString !== result.config.url ? `${queryString}`: null,
                  pathname: result.request.path,
                  path: result.request.path,
                  href: result.config.url
              },
              method: result.config.method.toUpperCase(),
              headers : axiosHeaders
            }
              
          }

          const statusCode = response.statusCode;
          const body = response.body;

          logger.debug(`downstream=ca-get-accountplans, statusCode=${statusCode}, request=${JSON.stringify(response.request)}, responseBody=${JSON.stringify(body)}`);
    
          const accountPlan = body ? body[0] : undefined;
          return resolve(accountPlan);
        }
        catch(error)
        {
          if(error.response)
          {
            const str = error.response.config.url
            const queryString = str?.slice(str.indexOf('?') + 1);
  
            const response = {
              statusCode: error.response.status,
              body: error.response.data,
              request:  
              {
                uri: {
                    protocol: error.response.request.protocol,
                    host: error.response.request.host,
                    port: error.response.request.agent.defaultPort,
                    hostname: error.response.request.agent.host,
                    auth: error.response.config.auth,
                    search: queryString !== error.response.config.url ? `?${queryString}`: null,
                    query: queryString !== error.response.config.url ? `${queryString}`: null,
                    pathname: error.response.request.path,
                    path: error.response.request.path,
                    href: error.response.config.url
                },
                method: error.config.method.toUpperCase(),
                headers : axiosHeaders
              },
              headers: error.response.headers
                
            }

            const statusCode = response.statusCode;
            const body = response.body;
  
  
            logger.debug(`downstream=ca-get-accountplans, statusCode=${statusCode}, request=${JSON.stringify(response.request)}, responseBody=${JSON.stringify(body)}`);

            logger.error(`ACCOUNT PLAN SERVER ERROR: ${JSON.stringify(body.error ? body.error : body)}`)
            return reject({type: 'generic_server_error', details: 'Generic server side error'});

    
          }
          else
          {
            logger.debug(`ACCOUNT PLAN REQUEST FAILED: ${JSON.stringify(error)}`);
            return reject({type: 'generic_server_error', details: error});
          }
          
        }

      }); // End Promise()
    };
    // Lookup in cache.
    return await accountPlanCache.get(cacheId, caLookup);
  }
}
