const axios = require('axios').default;
const https = require('https');
const { apimBasePath } = requireSrc('config');
const logger = requireSrc('log');

const getApplication = async (payload) => {
  logger.info(`getApplication()`);
  const url = `${apimBasePath}v1/portal/apim/applications/${payload.uuid}`;
  const httpsAgent = new https.Agent({ rejectUnauthorized: false });
  try{
    const response = await axios.get(url, { httpsAgent });
    
    if(response.status === 200) {
      return response.data;
    }
  }catch(error){
    logger.error(`getApplication() - error`, error);
    let errorType;
    if(error.response.status === 400) errorType = 'bad_request';
    else if(error.response.status === 401) errorType = 'unauthorized';
    else if(error.response.status === 404 && error.response.data.details.errorCode === '505') errorType = 'invalid_application';
    else errorType = 'generic_server_error';

    throw { type: errorType, details: error.response.data};
  }
};

const searchApplication = async (payload) => {
  logger.info(`searchApplication()`);
  const url = `${apimBasePath}v1/portal/apim/applications/search`;
  const httpsAgent = new https.Agent({ rejectUnauthorized: false });
  const response = await axios.post(url, payload, { httpsAgent });

  if(response.status === 200) {
    return response.data;
  }

  let errorType = 'generic_server_error';
  if(response.status === 400) errorType = 'bad_request';
  else if(response.status === 401) errorType = 'unauthorized';

  throw { type: errorType, details: response.status.details };
};

const getApplicationsForOrganization = async (payload) => {
  logger.info(`getApplicationsForOrganization()`);
  const url = `${apimBasePath}v1/portal/apim/applications/filter`;
  const httpsAgent = new https.Agent({ rejectUnauthorized: false });

  const requestConfig = {
    httpsAgent,
    headers: { organizationUuid: payload.organizationUuid }
  };

  const response = await axios.get(url, requestConfig);

  if(response.status === 200) {
    return response.data;
  }

  let errorType = 'generic_server_error';
  if(response.status === 400) errorType = 'bad_request';
  else if(response.status === 401) errorType = 'unauthorized';

  throw { type: errorType, details: response.status.details };
};

const createApplication = async (payload) => {
  logger.info(`createApplication()`);
  const url = `${apimBasePath}v1/portal/apim/applications`;
  const httpsAgent = new https.Agent({ rejectUnauthorized: false });
  const response = await axios.post(url, payload, { httpsAgent });

  if(response.status === 201) {
    return response.data;
  }

  let errorType = 'generic_server_error';
  if(response.status === 400) errorType = 'bad_request';
  else if(response.status === 401) errorType = 'unauthorized';

  throw { type: errorType, details: response.status.details };
};

const updateApplication = async (payload) => {
  logger.info(`updateApplication()`);
  const url = `${apimBasePath}v1/portal/apim/applications/${payload.Uuid}`;
  const httpsAgent = new https.Agent({ rejectUnauthorized: false });
  try{
    const response = await axios.put(url, payload, { httpsAgent });

    if(response.status === 200) {
      return response.data;
    }

  }catch(error){
    logger.error(`updateApplication() - error`, error);
    let errorType    
    if(error.response.status === 400 && error.response.data.details.errorCode === '483' && error.response.data.details.validationErrors[0].error === 'Application name is not unique.') errorType = 'duplicate_application_name';
    else if(error.response.status === 400) errorType = 'bad_request';
    else if(error.response.status === 401) errorType = 'unauthorized';
    else errorType = 'generic_server_error';

    throw { type: errorType, details: error.response.data };
  }
};

const promisifyUpdateApplicaion = (payload) => {
  return new Promise(async (resolve, reject) =>{
    try {
      const result = await updateApplication(payload);
      return resolve(result);
    } catch (error) {
      return reject(error);
    }
  });
};

const deleteApplication = async (payload) => {
  logger.info(`deleteApplication()`);
  const url = `${apimBasePath}v1/portal/apim/applications/${payload.uuid}`;
  const httpsAgent = new https.Agent({ rejectUnauthorized: false });
  const response = await axios.delete(url, { httpsAgent });

  if(response.status === 204) {
    return true;
  }

  let errorType = 'generic_server_error';
  if(response.status === 400) errorType = 'bad_request';
  else if(response.status === 401) errorType = 'unauthorized';

  throw { type: errorType, details: response.status.details };
};

const getApiGroups = async (payload) => {
  logger.info(`getApiGroups()`);
  const url = `${apimBasePath}v1/portal/apim/apigroups`;
  const httpsAgent = new https.Agent({ rejectUnauthorized: false });
  const response = await axios.get(url, { httpsAgent });

  if(response.status === 200) {
    return response.data;
  }

  let errorType = 'generic_server_error';
  if(response.status === 400) errorType = 'bad_request';
  else if(response.status === 401) errorType = 'unauthorized';

  throw { type: errorType, details: response.status.details };
};

const getCustomFieldsByName = async (payload) => {
  logger.info(`getCustomFieldsByName()`);
  const url = `${apimBasePath}v1/portal/apim/customfields/search`;
  const httpsAgent = new https.Agent({ rejectUnauthorized: false });
  const response = await axios.post(url, payload, { httpsAgent });

  if(response.status === 200) {
    const result = response.data ? response.data.reduce((field, item) => (field[item.Name] = item.Uuid, field), {}) : null;
    return result;
  }

  let errorType = 'generic_server_error';
  if(response.status === 400) errorType = 'bad_request';
  else if(response.status === 401) errorType = 'unauthorized';

  throw { type: errorType, details: response.status.details };
};

const postOrganization = async (payload) => {
  logger.info(`postOrganization()`);

  const url = `${apimBasePath}v1/portal/apim/organizations`;
  const httpsAgent = new https.Agent({ rejectUnauthorized: false });
  const response = await axios.post(url, payload, { httpsAgent });

  if(response.status === 201) {
    const result = response.data;
    return result;
  }

  let errorType = 'generic_server_error';
  if(response.status === 400) errorType = 'bad_request';
  else if(response.status === 401) errorType = 'unauthorized';

  throw { type: errorType, details: response.status.details };
}

module.exports = {
  getApplication,
  searchApplication,
  getApplicationsForOrganization,
  createApplication,
  updateApplication,
  deleteApplication,
  getApiGroups,
  getCustomFieldsByName,
  promisifyUpdateApplicaion,
  postOrganization,
};