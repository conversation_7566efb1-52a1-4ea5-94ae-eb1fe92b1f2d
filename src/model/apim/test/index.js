
const getApplication = async (payload) => {};

const searchApplication = async (payload) => {};

const getApplicationsForOrganization = async (payload) => {};

const createApplication = async (payload) => {};

const updateApplication = async (payload) => {};

const deleteApplication = async (payload) => {};

const getApiGroups = async (payload) => {};

const getCustomFieldsByName = async (payload) => {};

const promisifyUpdateApplicaion = async (payload) => {};

module.exports = {
  getApplication,
  searchApplication,
  getApplicationsForOrganization,
  createApplication,
  updateApplication,
  deleteApplication,
  getApiGroups,
  getCustomFieldsByName,
  promisifyUpdateApplicaion,
};
