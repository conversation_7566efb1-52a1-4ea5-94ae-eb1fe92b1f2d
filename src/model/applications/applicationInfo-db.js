const uuidv4 = require('uuid/v4')
const dayjs = require('dayjs')

const logger = requireSrc('log')
const { timestamp } = requireSrc('config')

const pool = require('../mysql-pool')
// DB Table
const tblApplicationsInfo = 'applications'

/**
 DB Column
 uuid
 portal_applicationId
 portal_userId
 created_at
 updated_at
 deleted_at
 */

module.exports = {
  /**
   * Lookup for applicationName that are 'ENABLED' or 'DISABLED' within an organization
   * @param  {[type]} applicationName  [user input applicationName]
   * @param  {[type]} organizationUuid [UUID]
   * @return {[type]}                  [description]
   */
  lookupNonDeletedApplicationNameIfExist: (applicationName, organizationUuid) => {
    logger.debug(`applications-db lookupNonDeletedApplicationNameIfExist()`)
    return new Promise(async (resolve, reject) => {
      const enabledStatus = `ENABLED`
      const disabledStatus = `DISABLED`
      const query = `SELECT * FROM ${tblApplicationsInfo} WHERE applicationName=${pool.escape(applicationName)} AND organizationUuid=${pool.escape(organizationUuid)} AND status=${pool.escape(enabledStatus)} OR status=${pool.escape(disabledStatus)}`
      logger.debug(`query: ${query}`)

      try {
        const rows = await pool.query(query)
        if (rows && rows[0]) {
          // Found applicationName exist
          logger.debug(`APPLICATION NAME ALREADY EXIST ${JSON.stringify(rows[0])}`)
          const failureObject = {
            type: 'duplicate_application_name',
            source: 'applications-db-lookupNonDeletedApplicationNameIfExist'
          }
          reject(failureObject)
        } else {
          // No applicationName found
          resolve()
        }
      } catch (error) {
        logger.error(`CUSTOMPORTAL DB ERROR: ${error}`)
        const failureObject = {
          type: 'database_error',
          source: 'applications-db-lookupNonDeletedApplicationNameIfExist'
        }
        reject(failureObject)
      }
    }) // End Promise()
  }, // End lookupNonDeletedApplicationNameIfExist()
  /**
   * Use this to retrieve number of applications belong to an organization that are not deleted applications
   * It will return number of application from this method
   * @param  {[type]} organizationUuid [UUID]
   * @return {[type]}                  [number]
   */
  countNonDeletedApplicationByOrganizationUuid: (organizationUuid) => {
    logger.debug(`applications-db countNonDeletedApplicationByOrganizationUuid()`)
    return new Promise(async (resolve, reject) => {
      const enabledStatus = `ENABLED`
      const disabledStatus = `DISABLED`
      const query = `SELECT COUNT(organizationUuid) AS numOfExistingApp FROM ${tblApplicationsInfo} WHERE organizationUuid=${pool.escape(organizationUuid)} AND status=${pool.escape(enabledStatus)} OR status=${pool.escape(disabledStatus)}`
      logger.debug(`query: ${query}`)

      try {
        const rows = await pool.query(query)
        resolve(rows[0])
      } catch (error) {
        logger.error(`CUSTOMPORTAL DB ERROR: ${error}`)
        const failureObject = {
          type: 'database_error',
          source: 'applications-db-lookupNonDeletedApplicationNameIfExist'
        }
        reject(failureObject)
      }
    }) // End Promise()
  }, // End countNonDeletedApplicationByOrganizationUuid()
  lookupApplicationUuidByStatus: (portal_applicationId, status) => {
    logger.debug(`applications-db lookupApplicationUuidByStatus()`)
    return new Promise(async (resolve, reject) => {
      const query = `SELECT * FROM ${tblApplicationsInfo} WHERE applicationUuid=${pool.escape(portal_applicationId)} AND status=${pool.escape(status)}`
      logger.debug(`query: ${query}`)

      try {
        const rows = await pool.query(query)
        resolve(rows)
      } catch (error) {
        logger.error(`CUSTOMPORTAL DB ERROR: ${error}`)
        const failureObject = {
          type: 'database_error',
          source: 'applications-db-lookupApplicationUuidByStatus'
        }
        reject(failureObject)
      }
    }) // End Promise()
  }, // End lookupApplicationUuidByStatus()
  /**
   * Create one application record in DB
   * @param  {[type]} payload [JSON Payload]
   * @return {[type]}         [description]
   */
  createOneApplicationRecord: (payload) => {
    logger.debug(`applications-db createOneApplicationRecord()`)
    return new Promise(async (resolve, reject) => {
      const query = `INSERT INTO ${tblApplicationsInfo} SET ?`
      logger.debug(`query: ${query}`)
      logger.debug(`payload: ${JSON.stringify(payload)}`)

      try {
        const result = await pool.query(query, payload)

        if (result.affectedRows) {
          resolve()
        } else {
          logger.error(`FAIL TO INSERT TO APPLICATIONS ERR: ${result}`)
          const failureObject = {
            type: 'database_error',
            source: 'applications-db-createOneApplicationRecord'
          }
          reject(failureObject)
        }
      } catch (error) {
        logger.error(`CUSTOMPORTAL DB ERROR: ${error}`)
        if (error.code && error.code === 'ER_DUP_ENTRY') {
          logger.error('DUPLICATED ENTRY')
          const failureObject = {
            type: 'duplicated_entry',
            source: 'applications-db-createOneApplicationRecord'
          }
          reject(failureObject)
        }
        const failureObject = {
          type: 'database_error',
          source: 'applications-db-createOneApplicationRecord'
        }
        reject(failureObject)
      }
    }) // End Promise()
  }, // End createOneApplicationRecord()
  /**
   * Delete an application from DB upon creation failed for rollback
   * @param  {[type]} portal_applicationId [UUID]
   * @return {[type]}                      [description]
   */
  removeOneApplicationRecordByUuid: (portal_applicationId) => {
    logger.debug(`applications-db removeOneApplicationRecordByUuid()`)
    return new Promise(async (resolve, reject) => {
      const query = `DELETE FROM ${tblApplicationsInfo} WHERE applicationUuid=${pool.escape(portal_applicationId)}`
      logger.debug(`query: ${query}`)

      try {
        const result = await pool.query(query)
        logger.debug(`removeOneApplicationRecordByUuid RESULT: ${JSON.stringify(result)}`)

        if (result.affectedRows > 0) {
          resolve()
        } else {
          logger.error(`removeOneApplicationRecordByUuid FAIL TO DELETE: ${JSON.stringify(result)}`)
          const failureObject = {
            type: 'invalid_application',
            source: 'applications-db-removeOneApplicationRecordByUuid'
          }
          reject(failureObject)
        }
      } catch (error) {
        logger.error(`CUSTOMPORTAL DB ERROR: ${error}`)
        const failureObject = {
          type: 'database_error',
          source: 'applications-db-removeOneApplicationRecordByUuid'
        }
        reject(failureObject)
      }
    }) // End Promise()
  }, // removeOneApplicationRecordByUuid()
  updateOneDeletedApplicationInfoByUuid: (portal_applicationId) => {
    logger.debug(`applications-db updateOneDeletedApplicationInfoByUuid()`)
    return new Promise(async (resolve, reject) => {
      const deletedStatus = 'DELETED'
      const epochTimestamp = dayjs().unix(Number)
      const query = `UPDATE ${tblApplicationsInfo} SET status=${pool.escape(deletedStatus)}, deleted_at=${epochTimestamp} WHERE applicationUuid=${pool.escape(portal_applicationId)}`
      logger.debug(`query: ${query}`)

      try {
        const result = await pool.query(query)
        logger.debug(`updateOneDeletedApplicationInfoByUuid RESULT: ${JSON.stringify(result)}`)

        if (result.affectedRows > 0) {
          resolve()
        } else {
          logger.error(`updateOneDeletedApplicationInfoByUuid FAIL TO DELETE: ${JSON.stringify(result)}`)
          const failureObject = {
            type: 'invalid_application',
            source: 'applications-db-updateOneDeletedApplicationInfoByUuid'
          }
          reject(failureObject)
        }
      } catch (error) {
        logger.error(`CUSTOMPORTAL DB ERROR: ${error}`)
        const failureObject = {
          type: 'database_error',
          source: 'applications-db-updateOneDeletedApplicationInfoByUuid'
        }
        reject(failureObject)
      }
    }) // End Promise()
  }, // End updateOneDeletedApplicationByUuid()
  updateOneApplicationInfoByUuid: (payload) => {
    logger.debug(`applications-db updateOneApplicationInfoByUuid()`)
    return new Promise(async (resolve, reject) => {
      const query = `UPDATE ${tblApplicationsInfo} SET ? WHERE applicationUuid=${pool.escape(payload.applicationUuid)}`
      logger.debug(`query: ${query}`)
      try {
        const result = await pool.query(query, payload)
        logger.debug(`updateOneApplicationInfoByUuid UPDATE RESULT: ${JSON.stringify(result)}`)

        if (result.affectedRows) {
          resolve()
        } else {
          logger.error(`CUSTOMPORTAL DB FAILED TO UPDATE APPLICATION INFO: ${result}`)
          const failureObject = {
            type: 'database_error',
            source: 'applications-db-updateOneApplicationInfoByUuid'
          }
          reject(failureObject)
        }
      } catch (error) {
        logger.error(`CUSTOMPORTAL DB ERROR: ${error}`)
        const failureObject = {
          type: 'database_error',
          source: 'applications-db-updateOneApplicationInfoByUuid'
        }
        reject(failureObject)
      }
    }) // End Promise()
  } // End updateOneApplicationInfoByUuid()
}
