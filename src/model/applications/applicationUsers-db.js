const uuidv4 = require('uuid/v4')
const dayjs = require('dayjs')
const logger = requireSrc('log')

const pool = require('../mysql-pool')
// DB Table
const tblApplicationUsers = 'application_users'

/**
 DB Column
 applicationUuid
 applicationName
 description
 apiKey
 keySecret
 callbackUrl
 organizationUuid
 status
 created_at
 updated_at
 deleted_at
 */

module.exports = {
  /**
   * User created an application or join, append their userId by the applicationId
   * @param {[type]} portal_applicationId [UUID]
   * @param {[type]} portal_userId        [UUID]
   */
  addOneApplicationUsers: (portal_applicationId, portal_userId) => {
    logger.debug(`applications_users-db addOneApplicationUsers()`)
    return new Promise(async (resolve, reject) => {
      const epochTimestamp = dayjs().unix(Number)

      const record = {
        uuid: uuidv4(),
        portal_applicationId: portal_applicationId,
        portal_userId: portal_userId,
        created_at: epochTimestamp,
        updated_at: epochTimestamp,
        deleted_at: null
      }

      const query = `INSERT INTO ${tblApplicationUsers} SET ?`
      logger.debug(`query: ${query}`)
      logger.debug(`record: ${JSON.stringify(record)}`)

      try {
        const result = await pool.query(query, record)

        if (result.affectedRows) {
          resolve()
        } else {
          logger.error(`FAIL TO INSERT TO APPLICATIONS ERR: ${result}`)
          const failureObject = {
            type: 'database_error',
            source: 'applications_users-db-addOneApplicationUsers'
          }
          reject(failureObject)
        }
      } catch (error) {
        logger.error(`CUSTOMPORTAL DB ERROR: ${error}`)
        const failureObject = {
          type: 'database_error',
          source: 'applications_users-db-addOneApplicationUsers'
        }
        reject(failureObject)
      }
    }) // End Promise()
  }, // End addOneApplicationUsers()
  /**
   * User that deleted a specific app on CA Portal side, update it application timestamp
   * @param  {[type]} portal_applicationId [description]
   * @return {[type]}                      [description]
   */
  updateOneDeletedApplicationUserByAppId: (portal_applicationId) => {
    logger.debug(`applications_users-db updateOneDeletedApplicationUserByAppId()`)
    return new Promise(async (resolve, reject) => {
      const epochTimestamp = dayjs().unix(Number)
      const query = `UPDATE ${tblApplicationUsers} SET deleted_at=${epochTimestamp} WHERE portal_applicationId=${pool.escape(portal_applicationId)}`
      logger.debug(`query: ${query}`)

      try {
        const results = await pool.query(query)
        if (results.affectedRows) {
          resolve()
        } else {
          logger.error(`FAIL TO UPDATE TO APPLICATIONS_USERS ERR: ${results}`)
          resolve()
        }
      } catch (error) {
        logger.error(`CUSTOMPORTAL DB ERROR: ${error}`)
        const failureObject = {
          type: 'database_error',
          source: 'applications_users-db-updateOneDeletedApplicationUserByAppId'
        }
        reject(failureObject)
      }
    }) // End Promise()
  }, // End updateOneDeletedApplicationUserByAppId()
  /**
   * In the event of rollback on creation, this is to delete the relationship from DB
   * @param  {[type]} portal_applicationId [UUID]
   * @return {[type]}                      [description]
   */
  removeApplicationUserRecordByAppId: (portal_applicationId) => {
    logger.debug(`applications_users-db removeApplicationUserRecordByAppId()`)
    return new Promise(async (resolve, reject) => {
      const query = `DELETE FROM ${tblApplicationUsers} WHERE portal_applicationId=${pool.escape(portal_applicationId)}`
      try {
        const rows = await pool.query(query)
        resolve(rows)
      } catch (error) {
        logger.error(`CUSTOMPORTAL DB ERROR: ${error}`)
        const failureObject = {
          type: 'database_error',
          source: 'user_info-removeApplicationUserRecordByAppId'
        }
        reject(failureObject)
      }
    }) // End Promise()
  } // End removeApplicationUserRecordByAppId()
}
