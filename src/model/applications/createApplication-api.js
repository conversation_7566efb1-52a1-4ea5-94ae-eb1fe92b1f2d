const {
  portalApiBasePath,
  portalApiPort,
  portalTenantId
} = requireSrc('config')
const logger = requireSrc('log')

const axios = require('axios');
const https = require('https');

module.exports = {
  /**
   * [Create an application]
   * @param  {[type]} oauthToken [description]
   * @param  {[type]} payload    [Create Application Payload]
   * @return {[type]}            [description]
   */
  createApplication: (oauthToken, payload) => {
    logger.debug(`createApplication Payload: ${JSON.stringify(payload)}`)
    return new Promise(async (resolve, reject) => {
      logger.info('createApplication()')
      const httpsAgent = new https.Agent({
        rejectUnauthorized: false
      })

      const axiosHeaders = {
        Authorization: oauthToken,
        'Content-Type': 'application/json;charset=utf-8'
      }

      try
      {
        const result = await axios.post(`${portalApiBasePath}:${portalApiPort}/${portalTenantId}/Applications`,
          payload,
          {
          headers: axiosHeaders,
          httpsAgent: httpsAgent,
        })
        const str = result.config.url
        const queryString = str?.slice(str.indexOf('?') + 1);
  
        const response = {
          statusCode: result.status,
          body: result.data,
          request:  
          {
            uri: {
                protocol: result.request.protocol,
                host: result.request.host,
                port: result.request.agent.defaultPort,
                hostname: result.request.agent.host,
                auth: result.config.auth,
                search: queryString !== result.config.url ? `?${queryString}`: null,
                query: queryString !== result.config.url ? `${queryString}`: null,
                pathname: result.request.path,
                path: result.request.path,
                href: result.config.url
            },
            method: result.config.method.toUpperCase(),
            headers : axiosHeaders
          }
            
        }

        logger.info(`createApplication() response - ${JSON.stringify(response)}`)
        resolve(response.body)
      
      }
      catch(error)
      {
        let failureObject = {
          type: 'generic_server_error',
          source: 'createApplication',
          details: 'Server error'
        }

        if(error.response)
        {
          const str = error.response.config.url
          const queryString = str?.slice(str.indexOf('?') + 1);

          const response = {
            statusCode: error.response.status,
            body: error.response.data,
            request:  
            {
              uri: {
                  protocol: error.response.request.protocol,
                  host: error.response.request.host,
                  port: error.response.request.agent.defaultPort,
                  hostname: error.response.request.agent.host,
                  auth: error.response.config.auth,
                  search: queryString !== error.response.config.url ? `?${queryString}`: null,
                  query: queryString !== error.response.config.url ? `${queryString}`: null,
                  pathname: error.response.request.path,
                  path: error.response.request.path,
                  href: error.response.config.url
              },
              method: error.config.method.toUpperCase(),
              headers : axiosHeaders
            },
            headers: error.response.headers
              
          }
          logger.info(`createApplication() response - ${JSON.stringify(response)}`)
          if (response.statusCode >= 400) {
            logger.error(`response error: ${JSON.stringify(response)}`)
  
            if (response.statusCode === 400) {
              // const responsePayload = JSON.parse(response)
              logger.error(`response.body.error.detail.errorCode: ${response.body.error.detail.errorCode}`)
              if (response.body.error.detail.errorCode === '483' && response.body.error.detail.validationErrors[0].field === 'Name') {
                failureObject.type = 'duplicate_application_name'
              }
              if (response.body.error.detail.errorCode === '483' && response.body.error.detail.validationErrors[0].field === 'OauthScope') {
                failureObject.type = 'exceeded_oauthscope_length'
              }
              logger.debug(`failureObject: ${JSON.stringify(failureObject)}`)
              reject(failureObject)
            }
  
            // NOTE: In the event when user OAuth doesn't have rights to create applications due to APIGroups
            // Example of an account binded with Account Plan doesn't have rights to the API Groups will throw StatusCode 405
            if (response.statusCode === 405) {
              failureObject.type = 'invalid_oauth'
              reject(failureObject)
            }
  
            if (response.statusCode === 500) {
              if (response.headers.hasOwnProperty('www-authenticate') && response.headers['www-authenticate'].includes('Bearer error')) {
                failureObject.type = 'invalid_oauth'
                reject(failureObject)
              } else {
                reject(failureObject)
              }
            }
            reject(failureObject)
          }

        }
        else
        {
          logger.error(`createApplication() error: ${JSON.stringify(error)}`)
          reject(failureObject)
        }
        
      }

    }) // End Promise()
  }
}
