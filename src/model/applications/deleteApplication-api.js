// const { exec } = require('child_process')
const axios = require('axios');
const https = require('https');

const { portalApiBasePath, portalTenantId, portalApiPort } = requireSrc('config')
const logger = requireSrc('log')

module.exports = {
  // Delete Application
  deleteApplication: (payload) => {
    logger.info(`deleteApplication()`)
    return new Promise(async(resolve, reject) => {
      const url = `${portalApiBasePath}:${portalApiPort}/${portalTenantId}/Applications('${payload.applicationUuid}')`
      logger.debug(`url: ${url}`)

      const httpsAgent = new https.Agent({
        rejectUnauthorized: false
      })

      const axiosHeaders = {
        'Authorization': payload.oauthToken,
        'Content-Type': 'application/json; charset=utf-8'
      }

      try
      {
        const result = await axios.delete(url,
          {
          headers: axiosHeaders,
          httpsAgent: httpsAgent,
        })
        const str = result.config.url
        const queryString = str?.slice(str.indexOf('?') + 1);
  
        const response = {
          statusCode: result.status,
          body: result.data,
          request:  
          {
            uri: {
                protocol: result.request.protocol,
                host: result.request.host,
                port: result.request.agent.defaultPort,
                hostname: result.request.agent.host,
                auth: result.config.auth,
                search: queryString !== result.config.url ? `?${queryString}`: null,
                query: queryString !== result.config.url ? `${queryString}`: null,
                pathname: result.request.path,
                path: result.request.path,
                href: result.config.url
            },
            method: result.config.method.toUpperCase(),
            headers : axiosHeaders
          }
            
        }

        logger.debug(`data: ${response.body}`)
        logger.debug(`headers: ${JSON.stringify(response.headers)}`)
            
        resolve()

      }
      catch(error)
      {
        let failureObject = {
          type: 'generic_server_error',
          source: 'deleteApplication'
        }

        if(error.response)
        {
          const str = error.response.config.url
          const queryString = str?.slice(str.indexOf('?') + 1);

          const response = {
            statusCode: error.response.status,
            body: error.response.data,
            request:  
            {
              uri: {
                  protocol: error.response.request.protocol,
                  host: error.response.request.host,
                  port: error.response.request.agent.defaultPort,
                  hostname: error.response.request.agent.host,
                  auth: error.response.config.auth,
                  search: queryString !== error.response.config.url ? `?${queryString}`: null,
                  query: queryString !== error.response.config.url ? `${queryString}`: null,
                  pathname: error.response.request.path,
                  path: error.response.request.path,
                  href: error.response.config.url
              },
              method: error.config.method.toUpperCase(),
              headers : axiosHeaders
            },
            headers: error.response.headers
              
          }

          logger.debug(`data: ${response.body}`)
          logger.debug(`headers: ${JSON.stringify(response.headers)}`)

          if (error || response.statusCode >= 400) {
            const jsonData = JSON.parse(response.body)
            logger.error(`deleteApplication() error - ${response}`)
  
            // NOTE: Invalid applicationUuid
            if (response.statusCode === 404 && jsonData.error.detail.errorCode === '505') {
              failureObject.type = 'invalid_application'
              reject(failureObject)
            }
  
            // NOTE: OAuth if invalid/expires
            if (response.statusCode === 500) {
              // NOTE: Check if error is undefined or empty
              if (response.headers.hasOwnProperty('www-authenticate') && (response.headers['www-authenticate'].includes('Bearer error') || response.headers['www-authenticate'].includes('bearer error'))) {
                failureObject.type = 'invalid_oauth'
                reject(failureObject)
              } else if (jsonData.hasOwnProperty('exception') && jsonData.exception.includes('com.layer7.portal.service.rest.security.PortalAccessDeniedException')) {
                failureObject.type = 'invalid_oauth'
                reject(failureObject)
              } else {
                reject(failureObject)
              }
            }
  
            if (jsonData.hasOwnProperty('error') && jsonData.error !== '') {
              failureObject.type = 'invalid_application'
              reject(failureObject)
            }
          }
        
        }
        else
        {
          logger.error(`deleteApplication() error - ${error}`)
          reject(failureObject)
        }
      }


    // logger.debug(`deleteApplication()`)
    // const args1 = "--insecure -H \"Content-Type:application/json\" -H \"Authorization:  " + payload.oauthToken + "\" -X DELETE "
    // const deleteAPI = `${portalApiBasePath}:${portalApiPort}/${portalTenantId}/Applications('{${payload.applicationUuid}}')`
    // const curlCmd = args1 + " \"" + deleteAPI + "\""
    //
    // logger.debug(`CURL COMMAND IS HERE - ${curlCmd}`)
    //
    // exec(`curl ${curlCmd}`, (error, stdout, stderr) => {
    //   logger.debug(`error: ${error}`)
    //   logger.debug(`stdout: ${stdout}`)
    //   logger.debug(`stderr: ${stderr}`)
    //   if (stdout === undefined || stdout === null || stdout === '') {
    //     resolve()
    //   } else if (JSON.parse(stdout).error != null) {
    //     reject(JSON.parse(stdout).error)
    //   }
    // }) // End exec()
    }) // End Promise()
  } // End deleteApplication()
}
