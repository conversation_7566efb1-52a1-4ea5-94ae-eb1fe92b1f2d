const {
  portalApiBasePath,
  portalApiPort,
  portalTenantId
} = requireSrc('config')
const axios = require('axios');
const https = require('https');

const logger = requireSrc('log')

module.exports = {
  /**
   * [Using CA Portal APIs to retreive API Group UUID]
   * @param  {[type]} oauthToken   [CA Portal OAuth Token]
   * @param  {[type]} apiGroupUuid [ApiGroup UUID String value]
   * @return {[type]}              [description]
   */
  retrieveAPIGroupsByUuid: (oauthToken, apiGroupUuid) => {
    return new Promise(async(resolve, reject) => {
      logger.info('retrieveAPIGroupsByUuid()')

      const httpsAgent = new https.Agent({
        rejectUnauthorized: false
      })

      const axiosHeaders = {
        Authorization: oauthToken,
        'Content-Type': 'application/json; charset=utf-8'
      } 

      try
      {
        const result = await axios.get(`${portalApiBasePath}:${portalApiPort}/${portalTenantId}/api-management/1.0/api-groups/${apiGroupUuid}`,
        {
          headers: axiosHeaders,
          httpsAgent: httpsAgent,
        })
        const str = result.config.url
        const queryString = str?.slice(str.indexOf('?') + 1);
  
        const response = {
          statusCode: result.status,
          body: result.data,
          request:  
          {
            uri: {
                protocol: result.request.protocol,
                host: result.request.host,
                port: result.request.agent.defaultPort,
                hostname: result.request.agent.host,
                auth: result.config.auth,
                search: queryString !== result.config.url ? `?${queryString}`: null,
                query: queryString !== result.config.url ? `${queryString}`: null,
                pathname: result.request.path,
                path: result.request.path,
                href: result.config.url
            },
            method: result.config.method.toUpperCase(),
            headers : axiosHeaders
          }
            
        }

        resolve(response.body)
      
      }
      catch(error)
      {
        let failureObject = {
          type: 'generic_server_error',
          source: 'getApiGroup-retrieveAPIGroupsByUuid'
        }

        if(error.response)
        {
          const str = error.response.config.url
          const queryString = str?.slice(str.indexOf('?') + 1);

          const response = {
            statusCode: error.response.status,
            body: error.response.data,
            request:  
            {
              uri: {
                  protocol: error.response.request.protocol,
                  host: error.response.request.host,
                  port: error.response.request.agent.defaultPort,
                  hostname: error.response.request.agent.host,
                  auth: error.response.config.auth,
                  search: queryString !== error.response.config.url ? `?${queryString}`: null,
                  query: queryString !== error.response.config.url ? `${queryString}`: null,
                  pathname: error.response.request.path,
                  path: error.response.request.path,
                  href: error.response.config.url
              },
              method: error.config.method.toUpperCase(),
              headers : axiosHeaders
            },
            headers: error.response.headers
              
          }

          if (response.statusCode >= 400) {
            logger.error(`[GET] /api-management/1.0/api-groups/${apiGroupUuid} statusCode: ${response.statusCode}: ${JSON.stringify(response)}`)
  
            // NOTE: OAuth if invalid/expires
            if (response.statusCode === 500) {
              // NOTE: Check if error is undefined or empty
              if (response.headers.hasOwnProperty('www-authenticate') && response.headers['www-authenticate'].includes('Bearer error')) {
                failureObject.type = 'invalid_oauth'
                reject(failureObject)
              } else {
                reject(failureObject)
              }
            }
  
            if (response.statusCode === 404 || response.statusCode === 400) {
              const responsePayload = JSON.parse(response.body)
              if (responsePayload.error.code === 'ValidationException') {
                failureObject.type = 'invalid_apigroup'
              }
              reject(failureObject)
            }
          } 

        }
        else
        {
          logger.error(`[GET] /api-management/1.0/api-groups/${apiGroupUuid} error: ${error}`)
          reject(error)
        }
        
      }


    })
  } // End retrieveAPIGroupsByUuid()
}
