const axios = require('axios');
const https = require('https');
// const { exec } = require('child_process')

const { portalApiBasePath, portalApiPort, portalTenantId } = requireSrc('config')
const logger = requireSrc('log')

module.exports = {
  /**
   * Get list of CA Portal Applications
   * @param  {[type]} oauthToken [description]
   * @return {[type]}            [description]
   */
  getApplication: (oauthToken) => {
    return new Promise(async(resolve, reject) => {
      logger.info('getApplication()')
      const httpsAgent = new https.Agent({
        rejectUnauthorized: false
      })

      const axiosHeaders = {
        Authorization: `${oauthToken}`,
        'Content-Type': 'application/json; charset=utf-8'
      }

      try
      {
        const result = await axios.get(`${portalApiBasePath}:${portalApiPort}/${portalTenantId}/Applications`,
        {
          headers: axiosHeaders,
          httpsAgent: httpsAgent,
        })
        const str = result.config.url
        const queryString = str?.slice(str.indexOf('?') + 1);
  
        const response = {
          statusCode: result.status,
          body: result.data,
          request:  
          {
            uri: {
                protocol: result.request.protocol,
                host: result.request.host,
                port: result.request.agent.defaultPort,
                hostname: result.request.agent.host,
                auth: result.config.auth,
                search: queryString !== result.config.url ? `?${queryString}`: null,
                query: queryString !== result.config.url ? `${queryString}`: null,
                pathname: result.request.path,
                path: result.request.path,
                href: result.config.url
            },
            method: result.config.method.toUpperCase(),
            headers : axiosHeaders
          }
            
        }

        logger.debug(`getApplication() response - ${JSON.stringify(response)}`)
        resolve(response.body)

      }
      catch(error)
      {
        let failureObject = {
          type: 'generic_server_error',
          source: 'getApplicatinInfo-getApplication'
        }

        if(error.response)
        {
          const str = error.response.config.url
          const queryString = str?.slice(str.indexOf('?') + 1);

          const response = {
            statusCode: error.response.status,
            body: error.response.data,
            request:  
            {
              uri: {
                  protocol: error.response.request.protocol,
                  host: error.response.request.host,
                  port: error.response.request.agent.defaultPort,
                  hostname: error.response.request.agent.host,
                  auth: error.response.config.auth,
                  search: queryString !== error.response.config.url ? `?${queryString}`: null,
                  query: queryString !== error.response.config.url ? `${queryString}`: null,
                  pathname: error.response.request.path,
                  path: error.response.request.path,
                  href: error.response.config.url
              },
              method: error.config.method.toUpperCase(),
              headers : axiosHeaders
            },
            headers: error.response.headers
              
          }
          logger.debug(`getApplication() response - ${JSON.stringify(response)}`)

          if (response.statusCode >= 400) {
            logger.error(`response.headers error: ${JSON.stringify(response.headers)}`)
            if (response.headers.hasOwnProperty('www-authenticate') && (response.headers['www-authenticate'].includes('bearer error') || response.headers['www-authenticate'].includes('Bearer error'))) {
              const failureObject = {
                type: 'invalid_oauth'
              }
              reject(failureObject)
            } else {
              logger.error(response)
              reject(response.body)
            }
          }

        }
        else
        {
          logger.error(error)
          failureObject.type = 'invalid_application'
          reject(failureObject)
        }
        
      }
    })
  },
  /**
   * Get CA Portal Application by UUID
   * @param  {[type]} payload [JSON Object]
   * @return {[type]}         [description]
   */
  getApplicationByUuid: (payload) => {
    return new Promise(async(resolve, reject) => {
      logger.info('getApplicationByUuid()')
      try {
        const url = `${portalApiBasePath}:${portalApiPort}/${portalTenantId}/Applications('${payload.applicationUuid}')`
        const headers = {
          'Authorization': payload.oauthToken,
          'Content-Type': 'application/json; charset=utf-8'
        }
        logger.debug(`url: ${url}`)
        logger.debug(`headers: ${JSON.stringify(headers)}`)

        const httpsAgent = new https.Agent({
          rejectUnauthorized: false
        })
  
        try
        {
          const result = await axios.get(url,
          {
            headers: headers,
            httpsAgent: httpsAgent,
          })
          const str = result.config.url
          const queryString = str?.slice(str.indexOf('?') + 1);
    
          const response = {
            statusCode: result.status,
            body: result.data,
            request:  
            {
              uri: {
                  protocol: result.request.protocol,
                  host: result.request.host,
                  port: result.request.agent.defaultPort,
                  hostname: result.request.agent.host,
                  auth: result.config.auth,
                  search: queryString !== result.config.url ? `?${queryString}`: null,
                  query: queryString !== result.config.url ? `${queryString}`: null,
                  pathname: result.request.path,
                  path: result.request.path,
                  href: result.config.url
              },
              method: result.config.method.toUpperCase(),
              headers : axiosHeaders
            }
              
          }
          logger.debug(`APPDATA: ${JSON.stringify(response.body)}`)
          logger.debug(`RESPONSE: ${JSON.stringify(response)}`)

          resolve(response.body)

  
        }
        catch(error)
        {
          let failureObject = {
            type: 'generic_server_error',
            source: 'getApplicatinInfo-getApplication'
          }
  
          if(error.response)
          {
            const str = error.response.config.url
            const queryString = str?.slice(str.indexOf('?') + 1);
  
            const response = {
              statusCode: error.response.status,
              body: error.response.data,
              request:  
              {
                uri: {
                    protocol: error.response.request.protocol,
                    host: error.response.request.host,
                    port: error.response.request.agent.defaultPort,
                    hostname: error.response.request.agent.host,
                    auth: error.response.config.auth,
                    search: queryString !== error.response.config.url ? `?${queryString}`: null,
                    query: queryString !== error.response.config.url ? `${queryString}`: null,
                    pathname: error.response.request.path,
                    path: error.response.request.path,
                    href: error.response.config.url
                },
                method: error.config.method.toUpperCase(),
                headers : axiosHeaders
              },
              headers: error.response.headers
                
            }
            logger.debug(`APPDATA: ${JSON.stringify(response.body)}`)
            logger.debug(`RESPONSE: ${JSON.stringify(response)}`)
  

            if (response.statusCode >= 400) {
              logger.error(`response header: ${JSON.stringify(response)}`)
              // NOTE: OAuth if invalid/expires
              if (response.statusCode === 500) {
                const dataJSON = response.body;
                // NOTE: Check if error is undefined or empty
                if (response.headers.hasOwnProperty('www-authenticate') && (response.headers['www-authenticate'].includes('bearer error') || response.headers['www-authenticate'].includes('Bearer error'))) {
                  failureObject.type = 'invalid_oauth'
                  reject(failureObject)
                } else if (dataJSON.hasOwnProperty('exception') && dataJSON.exception.includes('com.layer7.portal.service.rest.security.PortalAccessDeniedException')) {
                  failureObject.type = 'invalid_oauth'
                  reject(failureObject)
                } else {
                  reject(failureObject)
                }
              }
  
              if (response.statusCode === 404) {
                const responsePayload = response.body;
                if (responsePayload.error.code === 'ValidationException' || responsePayload.error.code === 'NotFoundException') {
                  failureObject.type = 'invalid_application'
                }
                reject(failureObject)
              }
            }
  
          }
          else
          {
            failureObject.type = 'invalid_application'
            logger.error(`GET getApplicationByUuid() error - ${error}`)
            reject(error)
          }
          
        }

    } catch(error) {
      logger.error(`getApplicationByUuid() error: ${error}`);
      reject(error);
    }

    //   const args1 = "--insecure -H \"Authorization: " + payload.OauthToken + "\" -H \"Content-Type:application/json; charset=utf-8'\" -X GET \'"
    //   const getAPI = `${portalApiBasePath}:${portalApiPort}/${portalTenantId}/Applications('{${payload.Uuid}}')`
    //
    //   const curlCmd = args1 + "\'" + " \"" + getAPI + "\""
    //   logger.debug(`CURL COMMAND IS HERE - ${curlCmd}`)
    //
    //   exec(`curl ${curlCmd}`, (error, stdout, stderr) => {
    //     logger.debug(`stdout: ${stdout}`)
    //     const stdoutJson = JSON.parse(stdout)
    //
    //     // NOTE: Successfully retrieve, stdout will return empty
    //     if (stdoutJson.hasOwnProperty('error')) {
    //       const failureObject = {
    //         type: 'invalid_oauth'
    //       }
    //       reject(failureObject)
    //     } else if (error !== null) {
    //       logger.error(stdout)
    //       reject(JSON.parse(stdout))
    //     } else {
    //       resolve(stdoutJson)
    //     }
    //   })
    }) // End Promise
  }, // End getApplicationByUuid()

  findApplicationByUuid: (payload) => {
    logger.info('findApplicationByUuid()')
    return new Promise(async(resolve, reject) => {

      const axiosHeaders = {
        'Authorization': payload.oauthToken
      }


      const httpsAgent = new https.Agent({
        rejectUnauthorized: false
      })

      try
      {
        const result = await axios.get(`${portalApiBasePath}:${portalApiPort}/${portalTenantId}/Applications('${payload.portalApplicationUuid}')`,
        {
          headers: axiosHeaders,
          httpsAgent: httpsAgent
        })
        const str = result.config.url
        const queryString = str?.slice(str.indexOf('?') + 1);
  
        const response = {
          statusCode: result.status,
          body: result.data,
          request:  
          {
            uri: {
                protocol: result.request.protocol,
                host: result.request.host,
                port: result.request.agent.defaultPort,
                hostname: result.request.agent.host,
                auth: result.config.auth,
                search: queryString !== result.config.url ? `?${queryString}`: null,
                query: queryString !== result.config.url ? `${queryString}`: null,
                pathname: result.request.path,
                path: result.request.path,
                href: result.config.url
            },
            method: result.config.method.toUpperCase(),
            headers : axiosHeaders
          }
            
        }

        const statusCode = response.statusCode;
        const body = response.body;
        
        logger.debug(`downstream=ca-get-application, statusCode=${statusCode}, request=${JSON.stringify(response.request)}, responseBody=${JSON.stringify(body)}`);
        
        return resolve(body);

      }
      catch(error)
      {

        if(error.response)
        {
          const str = error.response.config.url
          const queryString = str?.slice(str.indexOf('?') + 1);

          const response = {
            statusCode: error.response.status,
            body: error.response.data,
            request:  
            {
              uri: {
                  protocol: error.response.request.protocol,
                  host: error.response.request.host,
                  port: error.response.request.agent.defaultPort,
                  hostname: error.response.request.agent.host,
                  auth: error.response.config.auth,
                  search: queryString !== error.response.config.url ? `?${queryString}`: null,
                  query: queryString !== error.response.config.url ? `${queryString}`: null,
                  pathname: error.response.request.path,
                  path: error.response.request.path,
                  href: error.response.config.url
              },
              method: error.config.method.toUpperCase(),
              headers : axiosHeaders
            },
            headers: error.response.headers
              
          }

          const statusCode = response.statusCode;
          const body = response.body;
          
          logger.debug(`downstream=ca-get-application, statusCode=${statusCode}, request=${JSON.stringify(response.request)}, responseBody=${JSON.stringify(body)}`);
          
          if (statusCode >= 400) {
            logger.error(`APPLICATION SERVER ERROR: ${JSON.stringify(body.error ? body.error : body)}`);
            return reject({type: 'generic_server_error', details: 'Generic server side error'});
          }

        }
        else
        {
          logger.debug(`APPLICATION REQUEST FAILED: ${JSON.stringify(error)}`);
          return reject({type: 'generic_server_error', details: error});
        }
        
      }

    });
  },
  /**
   * Check if an application name is unique in CA Portal
   */
  findApplicationByNameAndOrganizationUuid: (payload) => {
    logger.info('findApplicationByNameAndOrganizationUuid()');
    return new Promise(async(resolve, reject) => {

      const axiosHeaders = {
        'Authorization': payload.oauthToken
      }

      const httpsAgent = new https.Agent({
        rejectUnauthorized: false
      })

      try
      {
        const result = await axios.get(`${portalApiBasePath}:${portalApiPort}/${portalTenantId}/Applications`,
        {
          params: {
            $filter: `OrganizationUuid eq '${payload.orgUuid}' and indexof(Name, '${payload.appName}') gt 0`
          },
          headers: axiosHeaders,
          httpsAgent: httpsAgent
        })
        const str = result.config.url
        const queryString = str?.slice(str.indexOf('?') + 1);
  
        const response = {
          statusCode: result.status,
          body: result.data,
          request:  
          {
            uri: {
                protocol: result.request.protocol,
                host: result.request.host,
                port: result.request.agent.defaultPort,
                hostname: result.request.agent.host,
                auth: result.config.auth,
                search: queryString !== result.config.url ? `?${queryString}`: null,
                query: queryString !== result.config.url ? `${queryString}`: null,
                pathname: result.request.path,
                path: result.request.path,
                href: result.config.url
            },
            method: result.config.method.toUpperCase(),
            headers : axiosHeaders
          }
            
        }

        const statusCode = response.statusCode;
        const body = response.body;
        
        logger.debug(`downstream=ca-get-application, statusCode=${statusCode}, request=${JSON.stringify(response.request)}, responseBody=${JSON.stringify(body)}`);

        const appNameLower = payload.appName.toLowerCase();
        const app = body ? body.find(item => item.Name.toLowerCase() === appNameLower) : undefined;
        return resolve(app);

      }
      catch(error)
      {

        if(error.response)
        {
          const str = error.response.config.url
          const queryString = str?.slice(str.indexOf('?') + 1);

          const response = {
            statusCode: error.response.status,
            body: error.response.data,
            request:  
            {
              uri: {
                  protocol: error.response.request.protocol,
                  host: error.response.request.host,
                  port: error.response.request.agent.defaultPort,
                  hostname: error.response.request.agent.host,
                  auth: error.response.config.auth,
                  search: queryString !== error.response.config.url ? `?${queryString}`: null,
                  query: queryString !== error.response.config.url ? `${queryString}`: null,
                  pathname: error.response.request.path,
                  path: error.response.request.path,
                  href: error.response.config.url
              },
              method: error.config.method.toUpperCase(),
              headers : axiosHeaders
            },
            headers: error.response.headers
              
          }

          const statusCode = response.statusCode;
          const body = response.body;
          
          logger.debug(`downstream=ca-get-application, statusCode=${statusCode}, request=${JSON.stringify(response.request)}, responseBody=${JSON.stringify(body)}`);
          
          if (statusCode >= 400) {
            logger.error(`APPLICATION SERVER ERROR: ${JSON.stringify(body.error ? body.error : body)}`);
            return reject({type: 'generic_server_error', details: 'Generic server side error'});
          }

        }
        else
        {
          logger.debug(`APPLICATION REQUEST FAILED: ${JSON.stringify(error)}`);
          return reject({type: 'generic_server_error', details: error});
        }
        
      }



    })
  }
}
