const { test } = requireSrc('config');

// Create Application API
const CreateApplication = require('./createApplication-api');
// Delete application API
const DeleteApplication = require('./deleteApplication-api');
// Update application API
const UpdateApplication = require('./updateApplication-api');
// Get Application API
const GetApplication = require('./getApplication-api');
// Get API Group Uuid API
const GetApiGroup = require('./getApiGroup-api');
// DB Connector for Applications DB
const ApplicationInfoDB = (test) ? require('./test/applicationInfo-db') : require('./applicationInfo-db');
// DB Connector for Application Users DB
const ApplicationUsersDB = (test) ? require('./test/applicationUsers-db') : require('./applicationUsers-db');

module.exports = {
  CreateApplication,
  DeleteApplication,
  UpdateApplication,
  GetApplication,
  GetApiGroup,
  ApplicationInfoDB,
  ApplicationUsersDB
} // ---- end module.exports ----
