// const { exec } = require('child_process')
const axios = require('axios');
const https = require('https');

const { portalApiBasePath, portalApiPort, portalTenantId } = requireSrc('config')
const logger = requireSrc('log')

module.exports = {
  // Update existing application
  updateApplication: (payload, oauthToken) => {
    return new Promise(async(resolve, reject) => {
      logger.info(`updateApplication()`)
      logger.info(`payload: ${JSON.stringify(payload)}`)
      const { __metadata, ...payloadUpdated } = payload; 
      const url = `${portalApiBasePath}:${portalApiPort}/${portalTenantId}/Applications('${payloadUpdated.Uuid}')`

      const httpsAgent = new https.Agent({
        rejectUnauthorized: false
      })

      const axiosHeaders = {
        'Authorization': oauthToken,
        'Content-Type': 'application/json; charset=utf-8'
      }

      try
      {
        const result = await axios.put(url,
          payloadUpdated,
          {
          headers: axiosHeaders,
          httpsAgent: httpsAgent,
        })
        const str = result.config.url
        const queryString = str?.slice(str.indexOf('?') + 1);
  
        const response = {
          statusCode: result.status,
          body: result.data,
          request:  
          {
            uri: {
                protocol: result.request.protocol,
                host: result.request.host,
                port: result.request.agent.defaultPort,
                hostname: result.request.agent.host,
                auth: result.config.auth,
                search: queryString !== result.config.url ? `?${queryString}`: null,
                query: queryString !== result.config.url ? `${queryString}`: null,
                pathname: result.request.path,
                path: result.request.path,
                href: result.config.url
            },
            method: result.config.method.toUpperCase(),
            headers : axiosHeaders
          }
            
        }

        logger.info(`updateApplication() data: ${JSON.stringify(response.body)}`)
        logger.info(`updateApplication() response: ${JSON.stringify(response)}`)

        resolve()
      }
      catch(error)
      {
        let failureObject = {
          type: 'generic_server_error',
          source: 'updateApplication'
        }

        if(error.response)
        {
          const str = error.response.config.url
          const queryString = str?.slice(str.indexOf('?') + 1);

          const response = {
            statusCode: error.response.status,
            body: error.response.data,
            request:  
            {
              uri: {
                  protocol: error.response.request.protocol,
                  host: error.response.request.host,
                  port: error.response.request.agent.defaultPort,
                  hostname: error.response.request.agent.host,
                  auth: error.response.config.auth,
                  search: queryString !== error.response.config.url ? `?${queryString}`: null,
                  query: queryString !== error.response.config.url ? `${queryString}`: null,
                  pathname: error.response.request.path,
                  path: error.response.request.path,
                  href: error.response.config.url
              },
              method: error.config.method.toUpperCase(),
              headers : axiosHeaders
            },
            headers: error.response.headers
              
          }

          logger.info(`updateApplication() data: ${JSON.stringify(response.body)}`)
          logger.info(`updateApplication() response: ${JSON.stringify(response)}`)

          
          if (error || response.statusCode >= 400) {
            if (response.statusCode === 400) {
              // const responsePayload = JSON.parse(response)
              logger.error(`response.body.error.detail.errorCode: ${response.body.error.detail.errorCode}`)
              if (response.body.error.detail.errorCode === '483') {
                failureObject.type = 'duplicate_application_name'
              }
              logger.debug(`failureObject: ${JSON.stringify(failureObject)}`)
              reject(failureObject)
            }
  
            // NOTE: In the event when user OAuth doesn't have rights to create applications due to APIGroups
            // Example of an account binded with Account Plan doesn't have rights to the API Groups will throw StatusCode 405
            if (response.statusCode === 405) {
              failureObject.type = 'invalid_oauth'
              reject(failureObject)
            }
  
            logger.error(`updateApplication() error - ${error}`)
            logger.error(`updateApplication() data - ${JSON.stringify(response.body)}`)
  
            if (error === '' || (response.statusCode === 500 && response.body.error === '')) {
              failureObject.type = 'invalid_oauth'
            }
            reject(failureObject)
          }

        }
        else
        {
          reject(failureObject)
        }
        
      }



      // const args1 = "--insecure -H \"Authorization: " + payload.OauthToken + "\" -H \"Content-Type:application/json; charset=utf-8\" -X PUT -d \'"
      // const updateAPI = `${portalApiBasePath}:${portalApiPort}/${portalTenantId}/Applications('{${payload.Uuid}}')`
      // logger.debug(`PUT BODY IS HERE - ${JSON.stringify(putBody)}`)
      // const curlCmd = args1 + JSON.stringify(putBody) + "\'" + " \"" + updateAPI + "\""
      //
      // logger.debug(`CURL COMMAND IS HERE - ${curlCmd}`)
      //
      // exec(`curl ${curlCmd}`, (error, stdout, stderr) => {
      //   logger.info(`stdout: ${stdout}`)
      //   // NOTE: Successfully updated application, stdout will return empty
      //   if (stdout !== '') {
      //     const stdoutJson = JSON.parse(stdout)
      //     let failureObject = {
      //       type: 'generic_server_error'
      //     }
      //
      //     if (stdoutJson.error === '') {
      //       failureObject.type = 'invalid_oauth'
      //     } else if (stdoutJson.error.code === 'ValidationException') {
      //       failureObject.type = 'invalid_param'
      //     }
      //
      //     return reject(failureObject)
      //   } else if (error !== null) {
      //     logger.error(stdout)
      //     return reject(JSON.parse(stdout))
      //   } else {
      //     resolve()
      //   }
      // }) // End exec()
    }) // End Promise()
  } // End updateApplication()
}
