const axios = require('axios');
const https = require('https');

const { portalApiBasePath, portalApiPort, portalTenantId } = requireSrc('config')
const logger = requireSrc('log');
const Cache = requireSrc('util/cache');
const customFieldCache = new Cache();

module.exports = {
  /**
   * Get custom field from CA Portal by name
   */
  
  getCustomFieldsByName: async (payload) => {
    logger.info('getCustomFieldsByName()');
    let filter = '';
    for (field of payload.fieldNames) {
      filter += `Name eq '${field}' or `;
    }
    filter = filter.slice(0, -4);
    return new Promise(async(resolve, reject) => {

      const httpsAgent = new https.Agent({
        rejectUnauthorized: false
      })

      const axiosHeaders = {
        'Authorization': payload.oauthToken
      }

      try
      {
        const result = await axios.get(`${portalApiBasePath}:${portalApiPort}/${portalTenantId}/CustomFields`,
        {
          params: {
            $filter: filter,
            $select: 'Uuid, Name'
          },
          headers: axiosHeaders,
          httpsAgent: httpsAgent,
        })
        const str = result.config.url
        const queryString = str?.slice(str.indexOf('?') + 1);
  
        const response = {
          statusCode: result.status,
          body: result.data,
          request:  
          {
            uri: {
                protocol: result.request.protocol,
                host: result.request.host,
                port: result.request.agent.defaultPort,
                hostname: result.request.agent.host,
                auth: result.config.auth,
                search: queryString !== result.config.url ? `?${queryString}`: null,
                query: queryString !== result.config.url ? `${queryString}`: null,
                pathname: result.request.path,
                path: result.request.path,
                href: result.config.url
            },
            method: result.config.method.toUpperCase(),
            headers : axiosHeaders
          }
            
        }

        const statusCode = response.statusCode;
        const body = response.body;
        logger.info(`downstream=ca-get-customfields, statusCode=${statusCode}, request=${JSON.stringify(response.request)}, responseBody=${JSON.stringify(body)}`);

        const customFields = body ? body.reduce((field, item) => (field[item.Name] = item.Uuid, field), {}) : null;
        return resolve(customFields);

      
      }
      catch(error)
      {

        if(error.response)
        {
          const str = error.response.config.url
          const queryString = str?.slice(str.indexOf('?') + 1);

          const response = {
            statusCode: error.response.status,
            body: error.response.data,
            request:  
            {
              uri: {
                  protocol: error.response.request.protocol,
                  host: error.response.request.host,
                  port: error.response.request.agent.defaultPort,
                  hostname: error.response.request.agent.host,
                  auth: error.response.config.auth,
                  search: queryString !== error.response.config.url ? `?${queryString}`: null,
                  query: queryString !== error.response.config.url ? `${queryString}`: null,
                  pathname: error.response.request.path,
                  path: error.response.request.path,
                  href: error.response.config.url
              },
              method: error.config.method.toUpperCase(),
              headers : axiosHeaders
            },
            headers: error.response.headers
              
          }

          const statusCode = response.statusCode;
          const body = response.body;
  
          logger.info(`downstream=ca-get-customfields, statusCode=${statusCode}, request=${JSON.stringify(response.request)}, responseBody=${JSON.stringify(body)}`);
  
          if (statusCode >= 400) {
            logger.error(`CUSTOM FIELD SERVER ERROR: ${JSON.stringify(body.error ? body.error : body)}`)
            return reject({type: 'generic_server_error', details: 'Generic server side error'});
          }

        }
        else
        {
          if((typeof error) === 'string') {
            logger.info(`CUSTOM FIELD REQUEST FAILED: ${error}`);
          } else {
            logger.info(`CUSTOM FIELD REQUEST FAILED: ${JSON.stringify(error)}`);
          }
          
          return reject({type: 'generic_server_error', details: error});
        }
        
      }


    });
  }
  
  /*
  
  // old method with caching
  // caching is depricated as of 2021-Feb release because it creates conflict when application is updated and then created
  // update uses only two custom fields whereas create uses five custom fields
  // after update and data stored in cache, custom field check response comes from cache with two fields which cause the create to fail
  // in the future, if caching is turned back on, stored cache should be cleared before getting custom field from CA for the create case

  getCustomFieldsByName: async (payload) => {
    logger.info('getCustomFieldsByName()');
    const cacheId = 'CustomFieldLookup';
    let filter = '';
    for (field of payload.fieldNames) {
      filter += `Name eq '${field}' or `;
    }
    filter = filter.slice(0, -4);
    const caLookup = () => {
      return new Promise((resolve, reject) => {
        request({
          method: 'GET',
          url: `${portalApiBasePath}:${portalApiPort}/${portalTenantId}/CustomFields`,
          headers: {
            'Authorization': payload.oauthToken
          },
          qs: {
            $filter: filter,
            $select: 'Uuid, Name'
          },
          insecure: true,
          rejectUnauthorized: false,
          json: true
        }, (error, response) => {
          if(error) {
            if((typeof error) === 'string') {
              logger.debug(`CUSTOM FIELD REQUEST FAILED: ${error}`);
            } else {
              logger.debug(`CUSTOM FIELD REQUEST FAILED: ${JSON.stringify(error)}`);
            }
            
            return reject({type: 'generic_server_error', details: error});
          }

          const statusCode = response.statusCode;
          const body = response.body;

          logger.debug(`downstream=ca-get-customfields, statusCode=${statusCode}, request=${JSON.stringify(response.request)}, responseBody=${JSON.stringify(body)}`);

          if (statusCode >= 400) {
            logger.error(`CUSTOM FIELD SERVER ERROR: ${JSON.stringify(body.error ? body.error : body)}`)
            return reject({type: 'generic_server_error', details: 'Generic server side error'});
          }
          
          let customFields = undefined;
          if(body) {
            customFields = {};
            for (let field of body) {
              customFields[field.Name] = field.Uuid;
            }
          }
          return resolve(customFields);
        });
      });
    };
    // Lookup in cache.
    return await customFieldCache.get(cacheId, caLookup);
  }
*/

}
