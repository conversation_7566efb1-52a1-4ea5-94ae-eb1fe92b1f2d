const { sandbox } = requireSrc('config')
const { test } = requireSrc('config');

const Apim = (test) ? require('./apim/test') : require('./apim');

const Applications = require('./applications')
const Merchants = (test) ? require('./merchants/test') : require('./merchants')
const MockCustomerProfile = require('./mockcustomerprofile')
const MockMerchantProfile = require('./mockmerchantprofile')
const Uploads = require('./uploads')
const Organizations = require('./organizations')
const CustomFields = require('./customfields')
const Scopes = require('./scopes')
const Token = require('./token')
const AccountPlans = require('./accountplans')
const Lookup = (sandbox) ? require('./lookup/sandbox') : require('./lookup')
const Transfer = require('./transfer')
module.exports = { Apim, Applications, Merchants, Uploads, MockCustomerProfile, MockMerchantProfile, Lookup, Organizations, Token, CustomFields, Scopes, AccountPlans, Transfer }
