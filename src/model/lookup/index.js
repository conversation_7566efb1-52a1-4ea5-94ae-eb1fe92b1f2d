const qs = require('querystring')

const LookupApi = require('./lookup-api')
const { validateResponse } = requireSrc('util/downstream')

const { CLSRequest } = Commons;

const Cache = requireSrc('util/cache')
const lookupCache = new Cache()

module.exports = {
    getOne: async (key) => {
        const myRequest = CLSRequest
        const language = (myRequest.get('language') || 'en').toLowerCase()
        const cacheId = `lookupGetOne-${key}-${language}`
        const lookupStoreByKey = async () => {
            const result = await LookupApi.getByKey(key)
            const { data, error } = validateResponse(result)
            if (error) throw { error, key }
            return data
        };
        return await lookupCache.get(cacheId, lookupStoreByKey)
    },
    search: async (params) => {
        const myRequest = CLSRequest
        const language = (myRequest.get('language') || 'en').toLowerCase()
        const query = qs.stringify(params)
        const cacheId = `lookupSearch-${query}-${language}`
        const lookupStoreSearch = async () => {
            const result = await LookupApi.searchByQuery(query)
            const { data, error } = validateResponse(result)
            if (error) throw { error, params }
            return data
        };
        return await  lookupCache.get(cacheId, lookupStoreSearch)
    },
    flush:  () => { lookupCache.flush(); },
    stats:  () => { return lookupCache.stats(); }
}
