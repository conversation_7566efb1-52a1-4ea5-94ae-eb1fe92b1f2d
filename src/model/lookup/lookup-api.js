const axios = require('axios');
const https = require('https');

const logger = requireSrc('log');

const { microServiceApiBasePath } = requireSrc('config');


const { CLSRequest } = Commons;

const pathRegex = /^http[s]*:\/\/|\/.*$/g;
const hostHeader = microServiceApiBasePath.replace(pathRegex, '');

module.exports = {
    getByKey: (key) => {
        return new Promise(async(resolve, reject) => {
            const myRequest = CLSRequest;

            const httpsAgent = new https.Agent({
                rejectUnauthorized: false
            })

            const axiosHeaders = {
                requestUId: myRequest.get('reqId'),
                endpoint: myRequest.get('endpoint'),
                'accept-language': myRequest.get('language') || 'en',
                host: hostHeader
            }

            try
            {
                const result = await axios.get(`${microServiceApiBasePath}v1/lookup/${key}`,
                {
                    headers: axiosHeaders,
                    httpsAgent: httpsAgent,
                })
                const str = result.config.url
                const queryString = str?.slice(str.indexOf('?') + 1);
        
                const response = {
                    statusCode: result.status,
                    body: result.data,
                    request:  
                    {
                        uri: {
                            protocol: result.request.protocol,
                            host: result.request.host,
                            port: result.request.agent.defaultPort,
                            hostname: result.request.agent.host,
                            auth: result.config.auth,
                            search: queryString !== result.config.url ? `?${queryString}`: null,
                            query: queryString !== result.config.url ? `${queryString}`: null,
                            pathname: result.request.path,
                            path: result.request.path,
                            href: result.config.url
                        },
                        method: result.config.method.toUpperCase(),
                        headers : axiosHeaders
                    }
                    
                }

                logger.debug(`downstream=lookup, statusCode=${response.statusCode}, responseBody=${JSON.stringify(response.body)}, request=${JSON.stringify(response.request)}`)
                return resolve(response)

            
            }
            catch(error)
            {


                if(error.response)
                {
                const str = error.response.config.url
                const queryString = str?.slice(str.indexOf('?') + 1);

                const response = {
                    statusCode: error.response.status,
                    body: error.response.data,
                    request:  
                    {
                    uri: {
                        protocol: error.response.request.protocol,
                        host: error.response.request.host,
                        port: error.response.request.agent.defaultPort,
                        hostname: error.response.request.agent.host,
                        auth: error.response.config.auth,
                        search: queryString !== error.response.config.url ? `?${queryString}`: null,
                        query: queryString !== error.response.config.url ? `${queryString}`: null,
                        pathname: error.response.request.path,
                        path: error.response.request.path,
                        href: error.response.config.url
                    },
                    method: error.config.method.toUpperCase(),
                    headers : axiosHeaders
                    },
                    headers: error.response.headers
                    
                }
                    logger.error(response)
                    return reject(response)



                }
                else
                {
                    logger.error(error)
                    return reject(error)
                }
                
            }            


        });
    },
    searchByQuery: (querystring) => {
        return new Promise(async(resolve, reject) => {
            const myRequest = CLSRequest;

            const httpsAgent = new https.Agent({
                rejectUnauthorized: false
            })

            const axiosHeaders = {
                requestUId: myRequest.get('reqId'),
                endpoint: myRequest.get('endpoint'),
                'accept-language': myRequest.get('language') || 'en',
                host: hostHeader
            }

            try
            {
                const result = await axios.get(`${microServiceApiBasePath}v1/lookup?${querystring}`,
                {
                    headers: axiosHeaders,
                    httpsAgent: httpsAgent,
                })
                const str = result.config.url
                const queryString = str?.slice(str.indexOf('?') + 1);
        
                const response = {
                    statusCode: result.status,
                    body: result.data,
                    request:  
                    {
                        uri: {
                            protocol: result.request.protocol,
                            host: result.request.host,
                            port: result.request.agent.defaultPort,
                            hostname: result.request.agent.host,
                            auth: result.config.auth,
                            search: queryString !== result.config.url ? `?${queryString}`: null,
                            query: queryString !== result.config.url ? `${queryString}`: null,
                            pathname: result.request.path,
                            path: result.request.path,
                            href: result.config.url
                        },
                        method: result.config.method.toUpperCase(),
                        headers : axiosHeaders
                    }
                    
                }

                logger.debug(`downstream=lookup-search, statusCode=${response.statusCode}, responseBody=${JSON.stringify(response.body)}, request=${JSON.stringify(response.request)}`)
                return resolve(response)

            
            }
            catch(error)
            {


                if(error.response)
                {
                const str = error.response.config.url
                const queryString = str?.slice(str.indexOf('?') + 1);

                const response = {
                    statusCode: error.response.status,
                    body: error.response.data,
                    request:  
                    {
                    uri: {
                        protocol: error.response.request.protocol,
                        host: error.response.request.host,
                        port: error.response.request.agent.defaultPort,
                        hostname: error.response.request.agent.host,
                        auth: error.response.config.auth,
                        search: queryString !== error.response.config.url ? `?${queryString}`: null,
                        query: queryString !== error.response.config.url ? `${queryString}`: null,
                        pathname: error.response.request.path,
                        path: error.response.request.path,
                        href: error.response.config.url
                    },
                    method: error.config.method.toUpperCase(),
                    headers : axiosHeaders
                    },
                    headers: error.response.headers
                    
                }
                    logger.error(response)
                    return reject(response)

                }
                else
                {
                    logger.error(error)
                    return reject(error)
                }
                
            }  


        });
    }
}
