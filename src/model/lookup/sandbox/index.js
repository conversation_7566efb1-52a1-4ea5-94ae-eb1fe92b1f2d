const msg = require('./msg');

module.exports = {
    getOne: (key) => {
        const entry = msg[key];
        if (entry)
            return entry;
        else
            return { error: { msg_key: 'invalid_downstream_error' } }
    },
    search: (params) => {
        const entries = [];
        for (var key in msg) {
            entries.push(msg[key]);
        }
        return entries;
    },
    flush: () => {},
    stats: () => {
        return {
            "hits": 0,
            "misses": 0,
            "keys": 0,
            "ksize": 0,
            "vsize": 0
        }
    }
}