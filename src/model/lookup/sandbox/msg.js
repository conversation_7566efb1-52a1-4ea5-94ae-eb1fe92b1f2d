module.exports = {
success: {
  http_code: 200,
  biz_code: 1000,
  description: "Success",
},
created: {
  http_code: 201,
  biz_code: 1000,
  description: "Account successfully created",
},
accepted: {
  http_code: 202,
  biz_code: 1000,
  description:
    "The request has been received and presumed to run in the background",
},
missing_param: {
  http_code: 400,
  biz_code: 1999,
  description: "Missing required parameters",
},
invalid_param: {
  http_code: 400,
  biz_code: 1999,
  description: "Invalid parameters entered",
},
invalid_session_validity: {
  http_code: 400,
  biz_code: 1999,
  description:
    "Invalid sessionValidityPeriod - Allowed value is 60000 - 1800000",
},
invalid_status_code: {
  http_code: 400,
  biz_code: 1999,
  description: "Invalid statusCode",
},
invalid_put_status_pending: {
  http_code: 400,
  biz_code: 1999,
  description:
    "Invalid Transaction Update - Cannot update transaction status to PENDING",
},
invalid_put_status_cond: {
  http_code: 400,
  biz_code: 1999,
  description: "Current Update operation not allowed",
},
invalid_metadata_amount: {
  http_code: 400,
  biz_code: 1999,
  description:
    "Invalid Metadata Update - Allowed maximum of 20 key-value pairs in one transaction",
},
empty_string: {
  http_code: 400,
  biz_code: 1999,
  description: "Empty string input not supported",
},
incorrect_payment_amount: {
  http_code: 400,
  biz_code: 1999,
  description: "Incorrect payment amount",
},
invalid_payment_status: {
  http_code: 400,
  biz_code: 1999,
  description: "Transaction status is not valid for payment",
},
reached_maximum_app: {
  http_code: 400,
  biz_code: 1999,
  description: "Reached maximum of applications",
},
session_expired: {
  http_code: 400,
  biz_code: 1999,
  description: "Session expired",
},
no_such_icon: {
  http_code: 404,
  biz_code: 1999,
  description: "No such icon",
},
invalid_trans_token: {
  http_code: 400,
  biz_code: 1999,
  description: "Invalid or expired transactionToken",
},
invalid_account_no: {
  http_code: 400,
  biz_code: 1999,
  description: "Invalid accounts",
},
generic_api_error: {
  http_code: 400,
  biz_code: 1999,
  description: "Payment API error code",
},
invalid_ref_number: {
  http_code: 400,
  biz_code: 1999,
  description: "Created transaction contains invalid reference numbers",
},
payment_hub_error: {
  http_code: 400,
  biz_code: 1999,
  description: "Payment Hub cannot be accessed",
},
unrecognized_field_name: {
  http_code: 400,
  biz_code: 1999,
  description:
    "Unrecognized field name was entered - Please check spelling, and/or refer to the API docs for correct name",
},
unauthorized: {
  http_code: 401,
  biz_code: 1999,
  description:
    "Unrecognized field name was entered - Please check spelling, and/or refer to the API docs for correct name",
},
forbidden: {
  http_code: 401,
  biz_code: 1999,
  description: "Invalid apikey provided",
},
invalid_entity_record: {
  http_code: 404,
  biz_code: 1999,
  description: "Requested entity record does not exist",
},
invalid_transaction_id: {
  http_code: 404,
  biz_code: 1999,
  description: "transactionId not found",
},
method_not_allowed: {
  http_code: 405,
  biz_code: 1999,
  description: "Wrong http_code method requested on endpoint",
},
generic_server_error: {
  http_code: 500,
  biz_code: 1999,
  description: "Generic server side error",
},
bad_gateway: {
  http_code: 502,
  biz_code: 1999,
  description: "Invalid response from upstream server",
},
service_unavailable: {
  http_code: 503,
  biz_code: 1999,
  description:
    "Server is currently unavailable because traffic overload or it is down for maintenance",
},
request_timeout: {
  http_code: 504,
  biz_code: 1999,
  description: "API Request Timeout",
},
not_supported: {
  http_code: 501,
  biz_code: 1901,
  description: "Feature is not supported",
},
invalid_downstream_response: {
  http_code: 502,
  biz_code: 1899,
  description: "Invalid response from downstream service",
},
invalid_oauth: {
  http_code: 401,
  biz_code: 1999,
  description: "Invalid OauthToken",
},
invalid_application: {
  http_code: 404,
  biz_code: 6130,
  description: "Invalid application"
},
duplicate_application_name: {
  http_code: 400,
  biz_code: 6127,
  description: "Application name is not unique"
}

};
