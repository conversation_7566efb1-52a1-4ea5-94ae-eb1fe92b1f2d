const axios = require('axios');
const https = require('https');

const logger = requireSrc('log')

const { microServiceApiBasePath } = requireSrc('config')

const { CLSRequest } = Commons;

const pathRegex = /^http[s]*:\/\/|\/.*$/g
const hostHeader = microServiceApiBasePath.replace(pathRegex, '')

module.exports = {
  getOneMerchantAPI: (portal_applicationId) => {
    logger.info(`getOneMerchantAPI()`)
    return new Promise(async(resolve, reject) => {
      const myRequest = CLSRequest

      try {
        const url = `${microServiceApiBasePath}v1/portal/merchants?applicationUuid=${portal_applicationId}`
        logger.debug(`url: ${url}`)

        const httpsAgent = new https.Agent({
          rejectUnauthorized: false
        })

        const axiosHeaders = {
          requestUId: myRequest.get('reqId'),
          endpoint: myRequest.get('endpoint'),
          'accept-language': myRequest.get('language') || 'en',
          host: hostHeader
        }

        try
        {
          const result = await axios.get(url,
            {
            headers: axiosHeaders,
            httpsAgent: httpsAgent,
          })
          const str = result.config.url
          const queryString = str?.slice(str.indexOf('?') + 1);
    
          const response = {
            statusCode: result.status,
            body: result.data,
            request:  
            {
              uri: {
                  protocol: result.request.protocol,
                  host: result.request.host,
                  port: result.request.agent.defaultPort,
                  hostname: result.request.agent.host,
                  auth: result.config.auth,
                  search: queryString !== result.config.url ? `?${queryString}`: null,
                  query: queryString !== result.config.url ? `${queryString}`: null,
                  pathname: result.request.path,
                  path: result.request.path,
                  href: result.config.url
              },
              method: result.config.method.toUpperCase(),
              headers : axiosHeaders
            }
              
          }

          logger.debug(`getOneMerchantAPI() response: ${JSON.stringify(response)}`)
          if (response.body.status.code === 1000) {
            resolve(response.body.data)
          } else {
            // NOTE: Return empty array when merchant not found
            resolve([])
          }
        }
        catch(error)
        {

          if(error.response)
          {
            const str = error.response.config.url
            const queryString = str?.slice(str.indexOf('?') + 1);

            const response = {
              statusCode: error.response.status,
              body: error.response.data,
              request:  
              {
                uri: {
                    protocol: error.response.request.protocol,
                    host: error.response.request.host,
                    port: error.response.request.agent.defaultPort,
                    hostname: error.response.request.agent.host,
                    auth: error.response.config.auth,
                    search: queryString !== error.response.config.url ? `?${queryString}`: null,
                    query: queryString !== error.response.config.url ? `${queryString}`: null,
                    pathname: error.response.request.path,
                    path: error.response.request.path,
                    href: error.response.config.url
                },
                method: error.config.method.toUpperCase(),
                headers : axiosHeaders
              },
              headers: error.response.headers
                
            }

            logger.debug(`getOneMerchantAPI() response: ${JSON.stringify(response)}`)
            resolve([])


          }
          else
          {
            logger.error(`getOneMerchantAPI() error - ${error}`)
            return reject(error)
          }
          
        }

        // resolve(mockResponse.data)
      } catch (error) {
        if (error) {
          logger.error(`getOneMerchantAPI() error - ${error}`)
          reject(error)
        }
      }
    }) // End Promise()
  }, // End getOneMerchantAPI()
  createMerchantAPI: (merchantsPayload) => {
    logger.info(`createMerchantAPI()`)
    return new Promise(async(resolve, reject) => {
      const myRequest = CLSRequest
      const url = `${microServiceApiBasePath}v1/portal/merchants`

      const httpsAgent = new https.Agent({
        rejectUnauthorized: false
      })

      const axiosHeaders = {
        requestUId: myRequest.get('reqId'),
        endpoint: myRequest.get('endpoint'),
        'accept-language': myRequest.get('language') || 'en',
        host: hostHeader
      }

      try
      {
        const result = await axios.post(url,
          merchantsPayload,
          {
          headers: axiosHeaders,
          httpsAgent: httpsAgent,
        })
        const str = result.config.url
        const queryString = str?.slice(str.indexOf('?') + 1);
  
        const response = {
          statusCode: result.status,
          body: result.data,
          request:  
          {
            uri: {
                protocol: result.request.protocol,
                host: result.request.host,
                port: result.request.agent.defaultPort,
                hostname: result.request.agent.host,
                auth: result.config.auth,
                search: queryString !== result.config.url ? `?${queryString}`: null,
                query: queryString !== result.config.url ? `${queryString}`: null,
                pathname: result.request.path,
                path: result.request.path,
                href: result.config.url
            },
            method: result.config.method.toUpperCase(),
            headers : axiosHeaders
          }
            
        }

        logger.debug(`createMerchantAPI() response: ${JSON.stringify(response)}`)
        if (response.body.status.code === 1000) {
          resolve(response.body.data)
        } else {
          logger.error(`fail to createMerchantAPI()`)
          resolve(response)
        }

      }
      catch(error)
      {

        if(error.response)
        {
          const str = error.response.config.url
          const queryString = str?.slice(str.indexOf('?') + 1);

          const response = {
            statusCode: error.response.status,
            body: error.response.data,
            request:  
            {
              uri: {
                  protocol: error.response.request.protocol,
                  host: error.response.request.host,
                  port: error.response.request.agent.defaultPort,
                  hostname: error.response.request.agent.host,
                  auth: error.response.config.auth,
                  search: queryString !== error.response.config.url ? `?${queryString}`: null,
                  query: queryString !== error.response.config.url ? `${queryString}`: null,
                  pathname: error.response.request.path,
                  path: error.response.request.path,
                  href: error.response.config.url
              },
              method: error.config.method.toUpperCase(),
              headers : axiosHeaders
            },
            headers: error.response.headers
              
          }

          logger.debug(`createMerchantAPI() response: ${JSON.stringify(response)}`)

          logger.error(`fail to createMerchantAPI()`)
          resolve(response)

        }
        else
        {
          logger.error(`createMerchantAPI() error - ${error}`)
          logger.error(error)
          return reject(error)
        }
        
      }      

    }) // End Promise()
  }, // End createMerchantAPI()
  deleteOneMerchantAPI: (merchantsId) => {
    logger.info(`deleteOneMerchantAPI()`)

    return new Promise(async(resolve, reject) => {
      const myRequest = CLSRequest
      const url = `${microServiceApiBasePath}v1/portal/merchants/${merchantsId}`
      logger.debug(`url: ${url}`)

      const httpsAgent = new https.Agent({
        rejectUnauthorized: false
      })

      const axiosHeaders = {
        requestUId: myRequest.get('reqId'),
        endpoint: myRequest.get('endpoint'),
        'accept-language': myRequest.get('language') || 'en',
        host: hostHeader
      }

      try
      {
        const result = await axios.delete(url,
          {
          headers: axiosHeaders,
          httpsAgent: httpsAgent,
        })
        const str = result.config.url
        const queryString = str?.slice(str.indexOf('?') + 1);
  
        const response = {
          statusCode: result.status,
          body: result.data,
          request:  
          {
            uri: {
                protocol: result.request.protocol,
                host: result.request.host,
                port: result.request.agent.defaultPort,
                hostname: result.request.agent.host,
                auth: result.config.auth,
                search: queryString !== result.config.url ? `?${queryString}`: null,
                query: queryString !== result.config.url ? `${queryString}`: null,
                pathname: result.request.path,
                path: result.request.path,
                href: result.config.url
            },
            method: result.config.method.toUpperCase(),
            headers : axiosHeaders
          }
            
        }

        logger.debug(`deleteOneMerchantAPI() response: ${JSON.stringify(response)}`)
        if (response.body.status.code === 1000) {
          resolve(response.body.data)
        } else {
          logger.error(`fail to deleteOneMerchantAPI()`)
          resolve(response)
        }

      }
      catch(error)
      {

        if(error.response)
        {
          const str = error.response.config.url
          const queryString = str?.slice(str.indexOf('?') + 1);

          const response = {
            statusCode: error.response.status,
            body: error.response.data,
            request:  
            {
              uri: {
                  protocol: error.response.request.protocol,
                  host: error.response.request.host,
                  port: error.response.request.agent.defaultPort,
                  hostname: error.response.request.agent.host,
                  auth: error.response.config.auth,
                  search: queryString !== error.response.config.url ? `?${queryString}`: null,
                  query: queryString !== error.response.config.url ? `${queryString}`: null,
                  pathname: error.response.request.path,
                  path: error.response.request.path,
                  href: error.response.config.url
              },
              method: error.config.method.toUpperCase(),
              headers : axiosHeaders
            },
            headers: error.response.headers
              
          }

          logger.debug(`deleteOneMerchantAPI() response: ${JSON.stringify(response)}`)

          logger.error(`fail to deleteOneMerchantAPI()`)
          resolve(response)

        }
        else
        {
          logger.error(`deleteOneMerchantAPI() error - ${error}`)
          logger.error(error)
          return reject(error)
        }
        
      }   

    }) // End Promise()
  }, // End deleteOneMerchantAPI()
  updateOneMerchantAPI: (merchantsId, merchantsPayload) => {
    logger.info(`updateOneMerchantAPI()`)
    return new Promise(async(resolve, reject) => {
      const myRequest = CLSRequest
      const url = `${microServiceApiBasePath}v1/portal/merchants/${merchantsId}`
      const updateMerchantsPayload = {
        merchants: [merchantsPayload]
      }

      logger.debug(`url: ${url}`)
      logger.debug(`merchantsPayload: ${JSON.stringify(merchantsPayload)}`)
      logger.debug(`updateMerchantsPayload: ${JSON.stringify(updateMerchantsPayload)}`)


      const httpsAgent = new https.Agent({
        rejectUnauthorized: false
      })

      const axiosHeaders = {
        requestUId: myRequest.get('reqId'),
        endpoint: myRequest.get('endpoint'),
        'accept-language': myRequest.get('language') || 'en',
        host: hostHeader
      }

      try
      {
        const result = await axios.put(url,
          updateMerchantsPayload,
          {
          headers: axiosHeaders,
          httpsAgent: httpsAgent,
        })
        const str = result.config.url
        const queryString = str?.slice(str.indexOf('?') + 1);
  
        const response = {
          statusCode: result.status,
          body: result.data,
          request:  
          {
            uri: {
                protocol: result.request.protocol,
                host: result.request.host,
                port: result.request.agent.defaultPort,
                hostname: result.request.agent.host,
                auth: result.config.auth,
                search: queryString !== result.config.url ? `?${queryString}`: null,
                query: queryString !== result.config.url ? `${queryString}`: null,
                pathname: result.request.path,
                path: result.request.path,
                href: result.config.url
            },
            method: result.config.method.toUpperCase(),
            headers : axiosHeaders
          }
            
        }
        logger.debug(`updateOneMerchantAPI() response: ${JSON.stringify(response)}`)
        if (response.body.status.code === 1000) {
          resolve(response.body.data)
        } else {
          logger.error(`fail to updateOneMerchantAPI()`)
          resolve(response)
        }

      }
      catch(error)
      {

        if(error.response)
        {
          const str = error.response.config.url
          const queryString = str?.slice(str.indexOf('?') + 1);

          const response = {
            statusCode: error.response.status,
            body: error.response.data,
            request:  
            {
              uri: {
                  protocol: error.response.request.protocol,
                  host: error.response.request.host,
                  port: error.response.request.agent.defaultPort,
                  hostname: error.response.request.agent.host,
                  auth: error.response.config.auth,
                  search: queryString !== error.response.config.url ? `?${queryString}`: null,
                  query: queryString !== error.response.config.url ? `${queryString}`: null,
                  pathname: error.response.request.path,
                  path: error.response.request.path,
                  href: error.response.config.url
              },
              method: error.config.method.toUpperCase(),
              headers : axiosHeaders
            },
            headers: error.response.headers
              
          }
          logger.debug(`updateOneMerchantAPI() response: ${JSON.stringify(response)}`)

          logger.error(`fail to updateOneMerchantAPI()`)
          resolve(response)
        }
        else
        {
          logger.error(`updateOneMerchantAPI() error - ${error}`)
          logger.error(error)
          return reject(error)
        }
        
      }       


    }) // End Promise()
  } // End updateOneMerchantAPI()
}

// const mockResponse = {
//   'status': {
//     'code': 1000,
//     'description': 'Success'
//   },
//   'data': [{
//     'merchantId': '834876476227335',
//     'merchantName': 'helloworld',
//     'billerId': '635957422658384',
//     'billerName': 'world hello',
//     'notifications': [{
//       'HJQ': 'sadadsad',
//       'default': true
//     }],
//     'portal_applicationId': '744976ca-352b-4568-8580-e1b5cac75084',
//     'referenceType': '2',
//     'created_at': 1550497896,
//     'updated_at': 1550497896,
//     'merchantPans': [
//       {
//         'uuid': '06365571-47e6-4a75-8f46-b122e7f876b4',
//         'merchantId': '834876476227335',
//         'merchantPan': '****************',
//         'cardCode': 'VSA',
//         'created_at': 1550497896,
//         'updated_at': 1550497896
//       },
//       {
//         'uuid': '777ebda0-3500-4bff-9615-fb50efabeacc',
//         'merchantId': '834876476227335',
//         'merchantPan': '****************',
//         'cardCode': 'VSA',
//         'created_at': 1550497896,
//         'updated_at': 1550497896
//       },
//       {
//         'uuid': '82d37f45-6475-4ff6-8c6c-97ffa3c7de9d',
//         'merchantId': '834876476227335',
//         'merchantPan': '****************',
//         'cardCode': 'VSA',
//         'created_at': 1550497896,
//         'updated_at': 1550497896
//       }]
//   }]
// }
//
// const mockFailResponse = {
//   'status': {
//     'code': 6131,
//     'description': 'Invalid merchant',
//     'details': {
//       'type': 'invalid_merchant'
//     }
//   }
// }
