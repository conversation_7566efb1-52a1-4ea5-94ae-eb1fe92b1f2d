const axios = require('axios');
const https = require('https');

const logger = requireSrc('log')

const { microServiceApiBasePath } = requireSrc('config')

const { CLSRequest } = Commons;

const pathRegex = /^http[s]*:\/\/|\/.*$/g
const hostHeader = microServiceApiBasePath.replace(pathRegex, '')

module.exports = {
  /**
   * Send APIs thru mslb to /portal/customers/profile to create customer profile
   * Available Type:
   * P1 - Thai Citizen, P7 - Alien, P8 - Foreigner
   * Example of payload: {"applicationId": portal_applicationUuid, "cardType": type}
   * @param  {[type]} profilePayload [description]
   * @param  {[type]} userUuid       [description]
   * @return {[type]}                [description]
   */
  createOneCustomerProfileAPI: (profilePayload, userUuid) => {
    logger.info(`createOneCustomerProfileAPI()`)
    return new Promise(async(resolve, reject) => {
      const myRequest = CLSRequest
      try {
        const url = `${microServiceApiBasePath}v1/portal/customers/profile`
        logger.debug(`url: ${url}`)

        const httpsAgent = new https.Agent({
          rejectUnauthorized: false
        })

        const axiosHeaders = {
          requestUId: myRequest.get('reqId'),
          endpoint: myRequest.get('endpoint'),
          useruuid: userUuid,
          'accept-language': myRequest.get('language') || 'en',
          host: hostHeader
        }

        try
        {
          const result = await axios.post(url,
            profilePayload,
            {
            headers: axiosHeaders,
            httpsAgent: httpsAgent,
          })
          const str = result.config.url
          const queryString = str?.slice(str.indexOf('?') + 1);
    
          const response = {
            statusCode: result.status,
            body: result.data,
            request:  
            {
              uri: {
                  protocol: result.request.protocol,
                  host: result.request.host,
                  port: result.request.agent.defaultPort,
                  hostname: result.request.agent.host,
                  auth: result.config.auth,
                  search: queryString !== result.config.url ? `?${queryString}`: null,
                  query: queryString !== result.config.url ? `${queryString}`: null,
                  pathname: result.request.path,
                  path: result.request.path,
                  href: result.config.url
              },
              method: result.config.method.toUpperCase(),
              headers : axiosHeaders
            }
              
          }

          logger.debug(`createOneCustomerProfileAPI() response: ${JSON.stringify(response)}`)
          if (response.body.status.code === 1000) {
            resolve(response.body.data)
          } else {
            logger.error(`Failed to create mock profile`)
            resolve()
          }
        
        }
        catch(error)
        {

          if(error.response)
          {
            const str = error.response.config.url
            const queryString = str?.slice(str.indexOf('?') + 1);

            const response = {
              statusCode: error.response.status,
              body: error.response.data,
              request:  
              {
                uri: {
                    protocol: error.response.request.protocol,
                    host: error.response.request.host,
                    port: error.response.request.agent.defaultPort,
                    hostname: error.response.request.agent.host,
                    auth: error.response.config.auth,
                    search: queryString !== error.response.config.url ? `?${queryString}`: null,
                    query: queryString !== error.response.config.url ? `${queryString}`: null,
                    pathname: error.response.request.path,
                    path: error.response.request.path,
                    href: error.response.config.url
                },
                method: error.config.method.toUpperCase(),
                headers : axiosHeaders
              },
              headers: error.response.headers
                
            }

            logger.error(`Failed to create mock profile`)
            resolve()

          }
          else
          {
            logger.error(`createOneCustomerProfileAPI() error - ${error}`)
            return reject(error)
          }
          
        }

      } catch (error) {
        if (error) {
          logger.error(`createOneCustomerProfileAPI() error - ${error}`)
          reject(error)
        }
      }
    }) // End Promise()
  }, // End createOneCustomerProfileAPI()
  getCustomerProfilesByAppIdAPI: (portal_applicationId) => {
    logger.info(`getCustomerProfilesByAppIdAPI()`)
    return new Promise(async(resolve, reject) => {
      const myRequest = CLSRequest
      try {
        const url = `${microServiceApiBasePath}v1/portal/customers/profile?applicationUuid=${portal_applicationId}`
        logger.debug(`url: ${url}`)

        const httpsAgent = new https.Agent({
          rejectUnauthorized: false
        })

        const axiosHeaders = {
          requestUId: myRequest.get('reqId'),
          endpoint: myRequest.get('endpoint'),
          'accept-language': myRequest.get('language') || 'en',
          host: hostHeader
        }

        try
        {
          const result = await axios.get(url,
            {
            headers: axiosHeaders,
            httpsAgent: httpsAgent,
          })
          const str = result.config.url
          const queryString = str?.slice(str.indexOf('?') + 1);
    
          const response = {
            statusCode: result.status,
            body: result.data,
            request:  
            {
              uri: {
                  protocol: result.request.protocol,
                  host: result.request.host,
                  port: result.request.agent.defaultPort,
                  hostname: result.request.agent.host,
                  auth: result.config.auth,
                  search: queryString !== result.config.url ? `?${queryString}`: null,
                  query: queryString !== result.config.url ? `${queryString}`: null,
                  pathname: result.request.path,
                  path: result.request.path,
                  href: result.config.url
              },
              method: result.config.method.toUpperCase(),
              headers : axiosHeaders
            }
              
          }

          logger.debug(`getCustomerProfilesByAppIdAPI() response: ${JSON.stringify(response)}`)
          
          if (response.body.status.code === 1000) {
            resolve(response.body.data)
          } else {
            logger.error(`Failed to retrieve customer mock profile`)
            resolve([])
          }
        
        }
        catch(error)
        {

          if(error.response)
          {
            const str = error.response.config.url
            const queryString = str?.slice(str.indexOf('?') + 1);

            const response = {
              statusCode: error.response.status,
              body: error.response.data,
              request:  
              {
                uri: {
                    protocol: error.response.request.protocol,
                    host: error.response.request.host,
                    port: error.response.request.agent.defaultPort,
                    hostname: error.response.request.agent.host,
                    auth: error.response.config.auth,
                    search: queryString !== error.response.config.url ? `?${queryString}`: null,
                    query: queryString !== error.response.config.url ? `${queryString}`: null,
                    pathname: error.response.request.path,
                    path: error.response.request.path,
                    href: error.response.config.url
                },
                method: error.config.method.toUpperCase(),
                headers : axiosHeaders
              },
              headers: error.response.headers
                
            }

            logger.debug(`getCustomerProfilesByAppIdAPI() response: ${JSON.stringify(response)}`)
            logger.error(`Failed to retrieve customer mock profile`)
            resolve([])

          }
          else
          {
            logger.error(`getCustomerProfilesByAppIdAPI() error - ${error}`)
            return reject(error)
          }
          
        }


      } catch (error) {
        if (error) {
          logger.error(`getCustomerProfilesByAppIdAPI() error - ${error}`)
          reject(error)
        }
      }
    }) // End Promise()
  }, // End getCustomerProfilesByAppIdAPI()
  /**
   * Delete a mock customer profile
   * @param  {[type]} bankRmid [14 digit bankRmid]
   * @param  {[type]} userUuid [uuid of the user]
   * @return {[type]}          [description]
   */
  deleteOneMockCustomerProfileByBankRmidAPI: (bankRmid, userUuid) => {
    logger.info(`deleteOneMockCustomerProfileByBankRmidAPI()`)
    return new Promise(async(resolve, reject) => {
      const myRequest = CLSRequest

      try {
        const url = `${microServiceApiBasePath}v1/portal/customers/profile?bankRmid=${bankRmid}`
        logger.debug(`url: ${url}`)

        const httpsAgent = new https.Agent({
          rejectUnauthorized: false
        })

        const axiosHeaders = {
          requestUId: myRequest.get('reqId'),
          endpoint: myRequest.get('endpoint'),
          useruuid: userUuid,
          'accept-language': myRequest.get('language') || 'en',
          host: hostHeader
        }

        try
        {
          const result = await axios.delete(url,
          {
            headers: axiosHeaders,
            httpsAgent: httpsAgent,
          })
          const str = result.config.url
          const queryString = str?.slice(str.indexOf('?') + 1);
    
          const response = {
            statusCode: result.status,
            body: result.data,
            request:  
            {
              uri: {
                  protocol: result.request.protocol,
                  host: result.request.host,
                  port: result.request.agent.defaultPort,
                  hostname: result.request.agent.host,
                  auth: result.config.auth,
                  search: queryString !== result.config.url ? `?${queryString}`: null,
                  query: queryString !== result.config.url ? `${queryString}`: null,
                  pathname: result.request.path,
                  path: result.request.path,
                  href: result.config.url
              },
              method: result.config.method.toUpperCase(),
              headers : axiosHeaders
            }
              
          }

          logger.debug(`deleteOneMockCustomerProfileByBankRmidAPI() response: ${JSON.stringify(response)}`)
          if (response.body.status.code === 1000) {
            resolve(response.body.data)
          } else {
            logger.error(`Failed to delete customer mock profile`)
            resolve()
          }         

        
        }
        catch(error)
        {

          if(error.response)
          {
            const str = error.response.config.url
            const queryString = str?.slice(str.indexOf('?') + 1);

            const response = {
              statusCode: error.response.status,
              body: error.response.data,
              request:  
              {
                uri: {
                    protocol: error.response.request.protocol,
                    host: error.response.request.host,
                    port: error.response.request.agent.defaultPort,
                    hostname: error.response.request.agent.host,
                    auth: error.response.config.auth,
                    search: queryString !== error.response.config.url ? `?${queryString}`: null,
                    query: queryString !== error.response.config.url ? `${queryString}`: null,
                    pathname: error.response.request.path,
                    path: error.response.request.path,
                    href: error.response.config.url
                },
                method: error.config.method.toUpperCase(),
                headers : axiosHeaders
              },
              headers: error.response.headers
                
            }

            logger.debug(`deleteOneMockCustomerProfileByBankRmidAPI() response: ${JSON.stringify(response)}`)
            logger.error(`Failed to delete customer mock profile`)
            resolve()


          }
          else
          {
            logger.error(`deleteOneMockCustomerProfileByBankRmidAPI() error - ${error}`)
            return reject(error)
          }
          
        }


      } catch (error) {
        if (error) {
          logger.error(`deleteOneMockCustomerProfileByBankRmidAPI() error - ${error}`)
          reject(error)
        }
      }
    }) // End Promise()
  }
}
