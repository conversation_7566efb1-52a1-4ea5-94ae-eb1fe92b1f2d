const axios = require('axios');
const https = require('https');
const logger = requireSrc('log')

const { microServiceApiBasePath } = requireSrc('config')

const { CLSRequest } = Commons;

const pathRegex = /^http[s]*:\/\/|\/.*$/g
const hostHeader = microServiceApiBasePath.replace(pathRegex, '')

module.exports = {
    /**
     * Send APIs thru mslb to /portal/maemanee/merchant/profile to create merchant profile
     * Available Type:
     * MT01 - Online, MT02 - Department, MT03 - Stand Alone
     * Example of payload: {"applicationId": portal_applicationUuid, "shopType": type}
     * @param  {[type]} merchantPayload [description]
     * @param  {[type]} userUuid       [description]
     * @return {[type]}                [description]
     */
    createOneMerchantProfileAPI: (merchantPayload, userUuid) => {
        logger.info(`createOneMerchantProfileAPI()`)
        return new Promise(async(resolve, reject) => {
            const myRequest = CLSRequest
            try {
                const url = `${microServiceApiBasePath}v1/portal/maemanee/merchant/profile`
                logger.debug(`url: ${url}`)

                const httpsAgent = new https.Agent({
                    rejectUnauthorized: false
                })

                const axiosHeaders = {
                    requestUId: myRequest.get('reqId'),
                    endpoint: myRequest.get('endpoint'),
                    useruuid: userUuid,
                    'accept-language': myRequest.get('language') || 'en',
                    host: hostHeader
                }

                try
                {
                    const result = await axios.post(url,
                    merchantPayload,
                    {
                        headers: axiosHeaders,
                        httpsAgent: httpsAgent,
                    })
                    const str = result.config.url
                    const queryString = str?.slice(str.indexOf('?') + 1);
            
                    const response = {
                        statusCode: result.status,
                        body: result.data,
                        request:  
                        {
                            uri: {
                                protocol: result.request.protocol,
                                host: result.request.host,
                                port: result.request.agent.defaultPort,
                                hostname: result.request.agent.host,
                                auth: result.config.auth,
                                search: queryString !== result.config.url ? `?${queryString}`: null,
                                query: queryString !== result.config.url ? `${queryString}`: null,
                                pathname: result.request.path,
                                path: result.request.path,
                                href: result.config.url
                            },
                            method: result.config.method.toUpperCase(),
                            headers : axiosHeaders
                        }
                        
                    }

                    logger.debug(`createOneMerchantProfileAPI() response: ${JSON.stringify(response)}`)
                    if (response.body.status.code === 1000) {
                        resolve(response.body.data)
                    } else {
                        logger.error(`Failed to create mock profile`)
                        resolve()
                    }
                
                }
                catch(error)
                {

                    if(error.response)
                    {
                        const str = error.response.config.url
                        const queryString = str?.slice(str.indexOf('?') + 1);

                        const response = {
                            statusCode: error.response.status,
                            body: error.response.data,
                            request:  
                            {
                            uri: {
                                protocol: error.response.request.protocol,
                                host: error.response.request.host,
                                port: error.response.request.agent.defaultPort,
                                hostname: error.response.request.agent.host,
                                auth: error.response.config.auth,
                                search: queryString !== error.response.config.url ? `?${queryString}`: null,
                                query: queryString !== error.response.config.url ? `${queryString}`: null,
                                pathname: error.response.request.path,
                                path: error.response.request.path,
                                href: error.response.config.url
                            },
                            method: error.config.method.toUpperCase(),
                            headers : axiosHeaders
                            },
                            headers: error.response.headers
                            
                        }

                        logger.debug(`createOneMerchantProfileAPI() response: ${JSON.stringify(response)}`)
                        logger.error(`Failed to create mock profile`)
                        resolve()
    
                    }
                    else
                    {
                        logger.error(`createOneMerchantProfileAPI() error - ${error}`)
                        return reject(error)
                    }
                    
                }

            } catch (error) {
                if (error) {
                    logger.error(`createOneMerchantProfileAPI() error - ${error}`)
                    reject(error)
                }
            }
        }) // End Promise()
    }, // End createOneMerchantProfileAPI()
    /**
     * Delete a mock customer profile within applicationUuid
     * @param  {[type]} merchantId [50 digit merchantId]
     * @param  {[type]} userUuid [uuid of the user]
     * @return {[type]}          [description]
     */
    deleteAllMockMerchantProfileByApplicationUuidAPI: (applicationUuid, userUuid) => {
        logger.info(`deleteAllMockMerchantProfileByApplicationUuidAPI()`)
        return new Promise(async(resolve, reject) => {
            const myRequest = CLSRequest
            try {
                const url = `${microServiceApiBasePath}v1/portal/maemanee/merchant/profile?applicationUuid=${applicationUuid}`
                logger.debug(`url: ${url}`)

                const httpsAgent = new https.Agent({
                    rejectUnauthorized: false
                })

                const axiosHeaders = {
                    requestUId: myRequest.get('reqId'),
                    endpoint: myRequest.get('endpoint'),
                    useruuid: userUuid,
                    'accept-language': myRequest.get('language') || 'en',
                    host: hostHeader
                }

                try
                {
                    const result = await axios.delete(url,
                    {
                        headers: axiosHeaders,
                        httpsAgent: httpsAgent,
                    })
                    const str = result.config.url
                    const queryString = str?.slice(str.indexOf('?') + 1);
            
                    const response = {
                        statusCode: result.status,
                        body: result.data,
                        request:  
                        {
                            uri: {
                                protocol: result.request.protocol,
                                host: result.request.host,
                                port: result.request.agent.defaultPort,
                                hostname: result.request.agent.host,
                                auth: result.config.auth,
                                search: queryString !== result.config.url ? `?${queryString}`: null,
                                query: queryString !== result.config.url ? `${queryString}`: null,
                                pathname: result.request.path,
                                path: result.request.path,
                                href: result.config.url
                            },
                            method: result.config.method.toUpperCase(),
                            headers : axiosHeaders
                        }
                        
                    }

                    logger.debug(`deleteAllMockMerchantProfileByApplicationUuidAPI() response: ${JSON.stringify(response)}`)
                    if (response.body.status.code === 1000 || response.body.status.code === 6139) {
                        resolve(response.body.data)
                    } else {
                        logger.error(`Failed to delete merchant mock profile`)
                        resolve()
                    }
                
                }
                catch(error)
                {

                    if(error.response)
                    {
                        const str = error.response.config.url
                        const queryString = str?.slice(str.indexOf('?') + 1);

                        const response = {
                            statusCode: error.response.status,
                            body: error.response.data,
                            request:  
                            {
                            uri: {
                                protocol: error.response.request.protocol,
                                host: error.response.request.host,
                                port: error.response.request.agent.defaultPort,
                                hostname: error.response.request.agent.host,
                                auth: error.response.config.auth,
                                search: queryString !== error.response.config.url ? `?${queryString}`: null,
                                query: queryString !== error.response.config.url ? `${queryString}`: null,
                                pathname: error.response.request.path,
                                path: error.response.request.path,
                                href: error.response.config.url
                            },
                            method: error.config.method.toUpperCase(),
                            headers : axiosHeaders
                            },
                            headers: error.response.headers
                            
                        }
                        logger.debug(`deleteAllMockMerchantProfileByApplicationUuidAPI() response: ${JSON.stringify(response)}`)

                        logger.error(`Failed to delete merchant mock profile`)
                        resolve()
    
                    }
                    else
                    {
                        logger.error(`deleteAllMockMerchantProfileByApplicationUuidAPI() error - ${error}`)
                        return reject(error)
                    }
                    
                }

            } catch (error) {
                if (error) {
                    logger.error(`deleteAllMockMerchantProfileByApplicationUuidAPI() error - ${error}`)
                    reject(error)
                }
            }
        }) // End Promise()
    }
}
