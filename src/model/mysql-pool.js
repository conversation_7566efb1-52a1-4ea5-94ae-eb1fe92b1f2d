const { promisify } = require('util')
const mysql = require('mysql')

const config = requireSrc('config')

// Set Up RDS Connection Pool
const rds_conf = config.rds || {}
rds_conf.user = process.env.DB_USER_PORTAL
rds_conf.password = process.env.DB_PASSWORD_PORTAL
rds_conf.port = process.env.DB_PORT || rds_conf.port || 3306

const pool = mysql.createPool(rds_conf)

pool.query = promisify(pool.query)

module.exports = pool
