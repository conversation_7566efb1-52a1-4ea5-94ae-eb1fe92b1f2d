const axios = require('axios');
const https = require('https');
const { portalApiBasePath, portalApiPort, portalTenantId } = requireSrc('config');
const logger = requireSrc('log');

module.exports = {
  /**
   * Get organization from CA Portal by name and return its UUID if found
   */
  getOrganizationByName: (payload) => {
    logger.info('getOrganizationByName()');
    logger.debug(`INPUT PAYLOAD FROM MODEL: ${JSON.stringify(payload)}`);
    return new Promise(async(resolve, reject) => {

      const httpsAgent = new https.Agent({
        rejectUnauthorized: false
      })

      const axiosHeaders = {
        'Authorization': payload.oauthToken
      }

      try
      {
        const result = await axios.get(`${portalApiBasePath}:${portalApiPort}/${portalTenantId}/Organizations`,
          {
            params: {
              $filter: `indexof(Name, '${payload.orgName}') gt 0`
            },
            headers: axiosHeaders,
            httpsAgent: httpsAgent,
        })
        const str = result.config.url
        const queryString = str?.slice(str.indexOf('?') + 1);
  
        const response = {
          statusCode: result.status,
          body: result.data,
          request:  
          {
            uri: {
                protocol: result.request.protocol,
                host: result.request.host,
                port: result.request.agent.defaultPort,
                hostname: result.request.agent.host,
                auth: result.config.auth,
                search: queryString !== result.config.url ? `?${queryString}`: null,
                query: queryString !== result.config.url ? `${queryString}`: null,
                pathname: result.request.path,
                path: result.request.path,
                href: result.config.url
            },
            method: result.config.method.toUpperCase(),
            headers : axiosHeaders
          }
            
        }

        const statusCode = response.statusCode;
        const body = response.body;

        logger.debug(`downstream=ca-get-organization, statusCode=${statusCode}, request=${JSON.stringify(response.request)}, responseBody=${JSON.stringify(body)}`);
        const orgNameLower = payload.orgName.toLowerCase();
        const org = body ? body.find(item => item.Name.toLowerCase() === orgNameLower) : undefined;
        return resolve(org);
      
      }
      catch(error)
      {

        if(error.response)
        {
          const str = error.response.config.url
          const queryString = str?.slice(str.indexOf('?') + 1);

          const response = {
            statusCode: error.response.status,
            body: error.response.data,
            request:  
            {
              uri: {
                  protocol: error.response.request.protocol,
                  host: error.response.request.host,
                  port: error.response.request.agent.defaultPort,
                  hostname: error.response.request.agent.host,
                  auth: error.response.config.auth,
                  search: queryString !== error.response.config.url ? `?${queryString}`: null,
                  query: queryString !== error.response.config.url ? `${queryString}`: null,
                  pathname: error.response.request.path,
                  path: error.response.request.path,
                  href: error.response.config.url
              },
              method: error.config.method.toUpperCase(),
              headers : axiosHeaders
            },
            headers: error.response.headers
              
          }

          const statusCode = response.statusCode;
          const body = response.body;
  
          logger.debug(`downstream=ca-get-organization, statusCode=${statusCode}, request=${JSON.stringify(response.request)}, responseBody=${JSON.stringify(body)}`);
          if (statusCode >= 400) {
            logger.error(`ORGANIZATION SERVER ERROR: ${JSON.stringify(body.error ? body.error : body)}`)
            return reject({type: 'generic_server_error', details: 'Generic server side error'});
          }

        }
        else
        {
          logger.debug(`ORGANIZATION REQUEST FAILED: ${JSON.stringify(error)}`);
          return reject({type: 'generic_server_error', details: error});
        }
        
      }


    })
  },

  getOrganizationByUuid: (payload) => {
    logger.info('getOrganizationByUuid()');
    return new Promise(async(resolve, reject) => {

      const httpsAgent = new https.Agent({
        rejectUnauthorized: false
      })

      const axiosHeaders = {
        'Authorization': payload.oauthToken
      }

      try
      {
        const result = await axios.get(`${portalApiBasePath}:${portalApiPort}/${portalTenantId}/Organizations('${payload.organizationUuid}')`,
          {
            headers: axiosHeaders,
            httpsAgent: httpsAgent,
        })
        const str = result.config.url
        const queryString = str?.slice(str.indexOf('?') + 1);
  
        const response = {
          statusCode: result.status,
          body: result.data,
          request:  
          {
            uri: {
                protocol: result.request.protocol,
                host: result.request.host,
                port: result.request.agent.defaultPort,
                hostname: result.request.agent.host,
                auth: result.config.auth,
                search: queryString !== result.config.url ? `?${queryString}`: null,
                query: queryString !== result.config.url ? `${queryString}`: null,
                pathname: result.request.path,
                path: result.request.path,
                href: result.config.url
            },
            method: result.config.method.toUpperCase(),
            headers : axiosHeaders
          }
            
        }

        const statusCode = response.statusCode;
        const body = response.body;
        
        logger.debug(`downstream=ca-get-organization, statusCode=${statusCode}, request=${JSON.stringify(response.request)}, responseBody=${JSON.stringify(body)}`);
        return resolve(body);
      
      }
      catch(error)
      {

        if(error.response)
        {
          const str = error.response.config.url
          const queryString = str?.slice(str.indexOf('?') + 1);

          const response = {
            statusCode: error.response.status,
            body: error.response.data,
            request:  
            {
              uri: {
                  protocol: error.response.request.protocol,
                  host: error.response.request.host,
                  port: error.response.request.agent.defaultPort,
                  hostname: error.response.request.agent.host,
                  auth: error.response.config.auth,
                  search: queryString !== error.response.config.url ? `?${queryString}`: null,
                  query: queryString !== error.response.config.url ? `${queryString}`: null,
                  pathname: error.response.request.path,
                  path: error.response.request.path,
                  href: error.response.config.url
              },
              method: error.config.method.toUpperCase(),
              headers : axiosHeaders
            },
            headers: error.response.headers
              
          }
          const statusCode = response.statusCode;
          const body = response.body;
          
          logger.debug(`downstream=ca-get-organization, statusCode=${statusCode}, request=${JSON.stringify(response.request)}, responseBody=${JSON.stringify(body)}`);
          if (statusCode >= 400) {
            logger.error(`ORGANIZATION SERVER ERROR: ${JSON.stringify(body.error ? body.error : body)}`);
            return reject({type: 'generic_server_error', details: 'Generic server side error'});
          }

        }
        else
        {
          logger.debug(`ORGANIZATION REQUEST FAILED: ${JSON.stringify(error)}`);
          return reject({type: 'generic_server_error', details: error});
        }
        
      }

    });
  }
}
