const axios = require('axios');
const https = require('https');

const { microServiceApiBasePath } = requireSrc('config')
const logger = requireSrc('log');

const pathRegex = /^http[s]*:\/\/|\/.*$/g;
const hostHeader = microServiceApiBasePath.replace(pathRegex, "");
const { CLSRequest } = Commons;

module.exports = {
  /**
   * Update scope
   */
  updateScope: (payload) => {
    logger.info('updateScope()');
    return new Promise(async(resolve, reject) => {
      const myRequest = CLSRequest;

      const httpsAgent = new https.Agent({
        rejectUnauthorized: false
      })

      const axiosHeaders = {
        "Content-Type": "application/json",
        requestUId: myRequest.get('reqId'),
        host: hostHeader
      }

      try
      {
        const result = await axios.put(`${microServiceApiBasePath}v1/scopes`,
          payload,
          {
          headers: axiosHeaders,
          httpsAgent: httpsAgent,
        })
        const str = result.config.url
        const queryString = str?.slice(str.indexOf('?') + 1);
  
        const response = {
          statusCode: result.status,
          body: result.data,
          request:  
          {
            uri: {
                protocol: result.request.protocol,
                host: result.request.host,
                port: result.request.agent.defaultPort,
                hostname: result.request.agent.host,
                auth: result.config.auth,
                search: queryString !== result.config.url ? `?${queryString}`: null,
                query: queryString !== result.config.url ? `${queryString}`: null,
                pathname: result.request.path,
                path: result.request.path,
                href: result.config.url
            },
            method: result.config.method.toUpperCase(),
            headers : axiosHeaders
          }
            
        }

        const statusCode = response.statusCode;
        const body = response.body;
        
        logger.debug(`downstream=update-scope, statusCode=${statusCode}, request=${JSON.stringify(response.request)}, responseBody=${JSON.stringify(body)}`);
        return resolve(body.data);
      }
      catch(error)
      {

        if(error.response)
        {
          const str = error.response.config.url
          const queryString = str?.slice(str.indexOf('?') + 1);

          const response = {
            statusCode: error.response.status,
            body: error.response.data,
            request:  
            {
              uri: {
                  protocol: error.response.request.protocol,
                  host: error.response.request.host,
                  port: error.response.request.agent.defaultPort,
                  hostname: error.response.request.agent.host,
                  auth: error.response.config.auth,
                  search: queryString !== error.response.config.url ? `?${queryString}`: null,
                  query: queryString !== error.response.config.url ? `${queryString}`: null,
                  pathname: error.response.request.path,
                  path: error.response.request.path,
                  href: error.response.config.url
              },
              method: error.config.method.toUpperCase(),
              headers : axiosHeaders
            },
            headers: error.response.headers
              
          }

          const statusCode = response.statusCode;
          const body = response.body;
          
          logger.debug(`downstream=update-scope, statusCode=${statusCode}, request=${JSON.stringify(response.request)}, responseBody=${JSON.stringify(body)}`);
  
          if (statusCode >= 400) {
            logger.error(`SCOPE SERVER ERROR: ${JSON.stringify(body.error ? body.error : body)}`);
            return reject({type: 'generic_server_error', details: 'Generic server side error'});
          }
        }
        else
        {
          logger.debug(`SCOPE REQUEST FAILED: ${JSON.stringify(error)}`);
          return reject({type: 'generic_server_error', details: error});
        }
        
      }


    });
  }
}
