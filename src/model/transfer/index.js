const { test } = requireSrc('config');
const TransferCoporateDB = (test) ? require('./test/transferCoperate-db') : require('./transferCoperate-db');
const TransferAccountDB = (test) ? require('./test/transferAccount-db') : require('./transferAccount-db');
const TransferBlacklistAccountDB = (test) ? require('./test/transferBlacklistAccount-db') : require('./transferBlacklistAccount-db');
const TransferAccountActivityDB = (test) ? require('./test/transferAccountActivity-db.js') : require('./transferAccountActivity-db.js');

module.exports = {
  TransferCoporateDB,
  TransferAccountDB,
  TransferBlacklistAccountDB,
  TransferAccountActivityDB
} // ---- end module.exports ----