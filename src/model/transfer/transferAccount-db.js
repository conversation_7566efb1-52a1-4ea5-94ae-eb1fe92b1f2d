const logger = requireSrc('log')
const pool = require('../mysql-pool')
// DB Table
const tblTransferAccount = 'transfer_account'

/**
 * Generate random citizen ID
 * @returns {string} - Random 13-digit citizen ID
 */
const generateRandomCitizenId = () => {
    const min = Math.pow(10, 12)
    const max = Math.pow(10, 13) - 1
    return Math.floor(Math.random() * (max - min + 1) + min).toString()
}

/**
 * Generate random account number with specified digits
 * @param {number} digits - Number of digits (10 or 13)
 * @returns {string} - Random account number
 */
const generateRandomAccountNumber = (digits) => {
    const min = Math.pow(10, digits - 1)
    const max = Math.pow(10, digits) - 1
    return Math.floor(Math.random() * (max - min + 1) + min).toString()
}

/**
 * Generate random person name
 * @returns {string} - Random person name
 */
const generateRandomPersonName = () => {
    const firstNames = ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>']
    const lastNames = ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>']
    
    const firstName = firstNames[Math.floor(Math.random() * firstNames.length)]
    const lastName = lastNames[Math.floor(Math.random() * lastNames.length)]
    
    return `${firstName} ${lastName}`
}

/**
 * DB Column
 * account
 * corporateId
 * status
 * accountName
 * balance
 * currency
 * citizenId
 * bankCode
 * applicationUuid
 * accountType
 * personType
 */
module.exports = {
    insertMockAccounts: (corporateId, applicationUuid) => {
        logger.debug(`transfer-account-db insertMockAccounts()`)
        
        return new Promise(async (resolve, reject) => {
            try {
                const mockAccounts = [
                    {
                        account: generateRandomAccountNumber(10),
                        corporateId: corporateId,
                        status: 'ACT',
                        accountName: generateRandomPersonName(),
                        balance: 200000,
                        currency: 'THB',
                        citizenId: generateRandomCitizenId(),
                        bankCode: '004',
                        applicationUuid: applicationUuid,
                        accountType: 'credit',
                        personType: 'payee'
                    },
                    {
                        account: generateRandomAccountNumber(10),
                        corporateId: corporateId,
                        status: 'ACT',
                        accountName: 'KE Bank',
                        balance: 200000,
                        currency: 'THB',
                        citizenId: generateRandomCitizenId(),
                        bankCode: '004',
                        applicationUuid: applicationUuid,
                        accountType: 'credit',
                        personType: 'payer'
                    },
                    {
                        account: generateRandomAccountNumber(10),
                        corporateId: corporateId,
                        status: 'ACT',
                        accountName: 'SCA Bank',
                        balance: 200000,
                        currency: 'THB',
                        citizenId: generateRandomCitizenId(),
                        bankCode: '014',
                        applicationUuid: applicationUuid,
                        accountType: 'credit',
                        personType: 'payer'
                    },
                    {
                        account: generateRandomAccountNumber(10),
                        corporateId: corporateId,
                        status: 'ACT',
                        accountName: generateRandomPersonName(),
                        balance: 200000,
                        currency: 'THB',
                        citizenId: generateRandomCitizenId(),
                        bankCode: '014',
                        applicationUuid: applicationUuid,
                        accountType: 'credit',
                        personType: 'payee'
                    },
                    {
                        account: generateRandomAccountNumber(13),
                        corporateId: corporateId,
                        status: 'ACT',
                        accountName: 'Golf hub Co. ltd.',
                        balance: 200000,
                        currency: 'USD',
                        citizenId: generateRandomCitizenId(),
                        bankCode: '014',
                        applicationUuid: applicationUuid,
                        accountType: 'credit',
                        personType: 'payer'
                    },
                    {
                        account: generateRandomAccountNumber(13),
                        corporateId: corporateId,
                        status: 'ACT',
                        accountName: generateRandomPersonName(),
                        balance: 200000,
                        currency: 'USD',
                        citizenId: generateRandomCitizenId(),
                        bankCode: '014',
                        applicationUuid: applicationUuid,
                        accountType: 'credit',
                        personType: 'payee'
                    },
                    {
                        account: generateRandomAccountNumber(10),
                        corporateId: corporateId,
                        status: 'ACT',
                        accountName: generateRandomPersonName(),
                        balance: 200000,
                        currency: 'THB',
                        citizenId: generateRandomCitizenId(),
                        bankCode: '014',
                        applicationUuid: applicationUuid,
                        accountType: 'debit',
                        personType: 'payer'
                    },
                    {
                        account: generateRandomAccountNumber(10),
                        corporateId: corporateId,
                        status: 'ACT',
                        accountName: 'Provincial Waterworks Authority',
                        balance: 200000,
                        currency: 'THB',
                        citizenId: generateRandomCitizenId(),
                        bankCode: '014',
                        applicationUuid: applicationUuid,
                        accountType: 'debit',
                        personType: 'payee'
                    },
                    {
                        account: generateRandomAccountNumber(13),
                        corporateId: corporateId,
                        status: 'ACT',
                        accountName: 'Netflux, Inc',
                        balance: 200000,
                        currency: 'USD',
                        citizenId: generateRandomCitizenId(),
                        bankCode: '014',
                        applicationUuid: applicationUuid,
                        accountType: 'debit',
                        personType: 'payee'
                    },
                    {
                        account: generateRandomAccountNumber(13),
                        corporateId: corporateId,
                        status: 'ACT',
                        accountName: generateRandomPersonName(),
                        balance: 200000,
                        currency: 'USD',
                        citizenId: generateRandomCitizenId(),
                        bankCode: '014',
                        applicationUuid: applicationUuid,
                        accountType: 'debit',
                        personType: 'payer'
                    }
                ]
                
                const insertPromises = mockAccounts.map(account => {
                    const query = `
                        INSERT INTO ${tblTransferAccount} 
                        (account, corporateId, status, accountName, balance, currency, citizenId, bankCode, applicationUuid, accountType, personType) 
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    `
                    const values = [
                        account.account,
                        account.corporateId,
                        account.status,
                        account.accountName,
                        account.balance,
                        account.currency,
                        account.citizenId,
                        account.bankCode,
                        account.applicationUuid,
                        account.accountType,
                        account.personType
                    ]
                    return pool.query(query, values)
                })
                await Promise.all(insertPromises)
                resolve()
            } catch (error) {
                logger.error(`CUSTOMPORTAL DB ERROR: ${error}`)
                const failureObject = {
                    type: 'database_error',
                    source: 'transfer-account-db-insertMockAccounts'
                }
                reject(failureObject)
            }
        })
    },
    getAccountByApplicationUuid:(applicationUuid) => {
        logger.debug(`transfer-account-db getAccountByApplicationUuid()`)
        return new Promise(async (resolve, reject) => {
        const query = `SELECT * FROM ${tblTransferAccount} WHERE applicationUuid=${pool.escape(applicationUuid)}`
        logger.debug(`query: ${query}`)
        try {
            const rows = await pool.query(query)
            resolve(rows)
        } catch (error) {
            logger.error(`CUSTOMPORTAL DB ERROR: ${error}`)
            const failureObject = {
            type: 'database_error',
            source: 'transfer-account-db-getAccountByApplicationUuid'
            }
            reject(failureObject)
        }
        }) // End Promise()
    },
    deleteAccountByAccountUuid:(applicationUuid) => {
        logger.debug(`transfer-account-db deleteAccountByAccountUuid()`)
        return new Promise(async (resolve, reject) => {
        const query = `DELETE FROM ${tblTransferAccount} WHERE applicationUuid=${pool.escape(applicationUuid)}`
        logger.debug(`query: ${query}`)
        try {
            const rows = await pool.query(query)
            resolve(rows)
        } catch (error) {
            logger.error(`CUSTOMPORTAL DB ERROR: ${error}`)
            const failureObject = {
            type: 'database_error',
            source: 'transfer-account-db-deleteAccountByAccountUuid'
            }
            reject(failureObject)
        }
        }) // End Promise()
    }
  }