const logger = requireSrc('log')
const pool = require('../mysql-pool')
const tblTransferAccountActivity = 'transfer_account_activity'

module.exports = {
    deleteActivityAccountByAccount:(account) => {
        logger.debug(`transfer-account-activity-db deleteActivityAccountByAccount()`)
        return new Promise(async (resolve, reject) => {
        const query = `DELETE FROM ${tblTransferAccountActivity} WHERE accountFrom IN (${pool.escape(account)}) or accountTo IN (${pool.escape(account)})`
        logger.debug(`query: ${query}`)

        try {
            const rows = await pool.query(query)
            resolve(rows)
        } catch (error) {
            logger.error(`CUSTOMPORTAL DB ERROR: ${error}`)
            const failureObject = {
            type: 'database_error',
            source: 'transfer-account-activity-db-deleteActivityAccountByAccount'
            }
            reject(failureObject)
        }
        })
    }
}