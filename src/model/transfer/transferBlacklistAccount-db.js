const logger = requireSrc('log')
const pool = require('../mysql-pool')
const tblTransferBlacklistAccount = 'transfer_blacklist_account'

module.exports = {
    deleteBlacklistAccountByAccount:(account) => {
        logger.debug(`transfer-blacklist-account-db deleteBlacklistAccountByAccount()`)
        return new Promise(async (resolve, reject) => {
        const query = `DELETE FROM ${tblTransferBlacklistAccount} WHERE account IN (${pool.escape(account)})`
        logger.debug(`query: ${query}`)

        try {
            const rows = await pool.query(query)
            resolve(rows)
        } catch (error) {
            logger.error(`CUSTOMPORTAL DB ERROR: ${error}`)
            const failureObject = {
            type: 'database_error',
            source: 'transfer-account-activity-db-transfer-blacklist-account-db deleteBlacklistAccountByAccount'
            }
            reject(failureObject)
        }
        })
    }
}