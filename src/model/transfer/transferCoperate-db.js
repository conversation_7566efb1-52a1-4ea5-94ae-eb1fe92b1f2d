const logger = requireSrc('log')
const pool = require('../mysql-pool')
// DB Table
const tblTransferCorporate = 'transfer_corporate'

/**
 DB Column
 corporateId
 corporateName
 */

module.exports = {
  getTransferCoperate: () => {
    logger.debug(`transfer-coperate-db getTransferCoperate()`)
    return new Promise(async (resolve, reject) => {
      const query = `SELECT * FROM ${tblTransferCorporate}`
      logger.debug(`query: ${query}`)
      try {
        const rows = await pool.query(query)
        const results = rows.map(row => ({ ...row }))
        resolve(results)
      } catch (error) {
        logger.error(`CUSTOMPORTAL DB ERROR: ${error}`)
        const failureObject = {
          type: 'database_error',
          source: 'transfer-corporate-db-getTransferCoperate'
        }
        reject(failureObject)
      }
    })
  }
}
