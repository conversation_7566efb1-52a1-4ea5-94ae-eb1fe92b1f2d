const axios = require('axios');
const https = require('https');
const logger = requireSrc('log')

const { microServiceApiBasePath } = requireSrc('config')

const { CLSRequest } = Commons;

const pathRegex = /^http[s]*:\/\/|\/.*$/g
const hostHeader = microServiceApiBasePath.replace(pathRegex, '')

module.exports = {
  getAppIconURL: (portal_applicationId) => {
    logger.info(`getAppIconURL()`)
    return new Promise(async(resolve, reject) => {
      const myRequest = CLSRequest
      try {
        const url = `${microServiceApiBasePath}v1/portal/uploads/icons/applications/${portal_applicationId}`

      const httpsAgent = new https.Agent({
        rejectUnauthorized: false
      })

      const axiosHeaders = {
        requestUId: myRequest.get('reqId'),
        endpoint: myRequest.get('endpoint'),
        'accept-language': myRequest.get('language') || 'en',
        host: hostHeader
      }

      try
      {
        const result = await axios.get(url,
        {
          headers: axiosHeaders,
          httpsAgent: httpsAgent,
        })
        const str = result.config.url
        const queryString = str?.slice(str.indexOf('?') + 1);
  
        const response = {
          statusCode: result.status,
          body: result.data,
          request:  
          {
            uri: {
                protocol: result.request.protocol,
                host: result.request.host,
                port: result.request.agent.defaultPort,
                hostname: result.request.agent.host,
                auth: result.config.auth,
                search: queryString !== result.config.url ? `?${queryString}`: null,
                query: queryString !== result.config.url ? `${queryString}`: null,
                pathname: result.request.path,
                path: result.request.path,
                href: result.config.url
            },
            method: result.config.method.toUpperCase(),
            headers : axiosHeaders
          }
            
        }
        logger.debug(`data: ${JSON.stringify(response.body)}`)
        logger.debug(`response: ${JSON.stringify(response)}`)
        if (response.body.status.code === 1000) {
          // Remove applicationUuid
          delete response.body.data.applicationUuid
          resolve(response.body.data)
        } else {
          resolve('')
        }
      }
      catch(error)
      {

        if(error.response)
        {
          const str = error.response.config.url
          const queryString = str?.slice(str.indexOf('?') + 1);

          const response = {
            statusCode: error.response.status,
            body: error.response.data,
            request:  
            {
              uri: {
                  protocol: error.response.request.protocol,
                  host: error.response.request.host,
                  port: error.response.request.agent.defaultPort,
                  hostname: error.response.request.agent.host,
                  auth: error.response.config.auth,
                  search: queryString !== error.response.config.url ? `?${queryString}`: null,
                  query: queryString !== error.response.config.url ? `${queryString}`: null,
                  pathname: error.response.request.path,
                  path: error.response.request.path,
                  href: error.response.config.url
              },
              method: error.config.method.toUpperCase(),
              headers : axiosHeaders
            },
            headers: error.response.headers
              
          }

          logger.debug(`data: ${JSON.stringify(response.body)}`)
          logger.debug(`response: ${JSON.stringify(response)}`)

          resolve('')

        }
        else
        {
          logger.error(error)
          return reject(error)
        }
        
      }


        // resolve(mockResponse.data)
      } catch (error) {
        if (error) {
          logger.error(`getAppIconURL() error - ${error}`)
          reject(error)
        }
      }
    }) // End Promise()
  } // End getAppIconURL()
}

const mockResponse = {
  'status': {
    'code': 1000,
    'description': 'Success'
  },
  'data': {
    'url': 'https://partner-eco-dev.s3.ap-southeast-1.amazonaws.com/portal-application-icon/54a84058-9560-46fb-a08d-8efbecad2def.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=ASIAR46RKPM7CQFLJSU4%2F20190227%2Fap-southeast-1%2Fs3%2Faws4_request&X-Amz-Date=20190227T150954Z&X-Amz-Expires=3600&X-Amz-Security-Token=FQoGZXIvYXdzEND%2F%2F%2F%2F%2F%2F%2F%2F%2F%2FwEaDB3Pyzh6sEm0b6ToAiKpBJi7zAmaFkREFO%2B23LUiyDM8m%2FggmLieNCCJiCGPvBq1bM6UMWkEnenNqxUGAfvBD3x0YuRi1xzn0jt5%2F30IYPUzicAeruI5pjYMkURD3D9vLyjOGpDtn6EsGLt3x63Sg%2BD248%2B9obpPJPucUqe3u0sDcsh0yz%2FX9sITHF532BgHI7RZnd2enzrxS8jqAbydGgN2%2Bf9R%2FqNKSOQ%2F1ZCtUN%2FJMa3cMpbe0zJhwXjHQg%2B%2BQXuTaAG7AvNOiWymg3FSDwFeQogk7VAVqRJIUxKbVd6rsMN%2FOlTBw3yu6LAuM7STTsKpuJ4TaItoYqF5%2BS8JSMQdhkL6ptU9Tk6YMz6KjMRtGsTCn4aHl8Bt16tNoClFhdyztSH0bDKf2uEqVdCf4K7lD%2BIVUk5Q3iVQ5ZW3BLaUWvYJkp%2Fp44%2FQG6zX7AQnFOQfrtUMkrO6PkpKmOQnxoZCcrxr1L8gzQWQCV6Xr%2BhN8iVlvim%2BtnN0UPwXdYRZJ%2FlIdvDSERAWv4cdvoGDzpuijqXEmq%2BE0%2BaOO25pAFT93O%2FbuSfapETe%2FErUaqLLzFOiihubylpuzobw8VEy0ByexIp5ewCtbFkOTwU7v2MytHRsJ48x5lRPjdvlu%2BD%2FJD8kb5U0UvhcfEwiDEFcyfo7u4SqKD8U0%2FLwMUN0oBUS6tfZYiJS%2FbIsIFrMCwAGSv%2BzgAUXsR0wMsvHXevg%2B%2BYs0oYTiaDaecSkdYcXGqHv5g5CyHZUJNAo7rva4wU%3D&X-Amz-Signature=d6b065febf5c03e24029495f29fb4eed0438cce45483b224e64d8a7d81f42cc5&X-Amz-SignedHeaders=host'
  }
}

const mockInvalidResponse = {
  'status': {
    'code': 1102,
    'description': 'Invalid parameters entered'
  },
  'data': {
    'message': 'applicationUuid is invalid UUID'
  }
}
