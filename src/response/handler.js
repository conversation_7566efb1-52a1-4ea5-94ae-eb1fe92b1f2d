const { Response } = Commons;

module.exports = {
  successResponse: async (res, next, msg_key, data) => {
    const response = new Response().bindContext(res);
    
    return response.success(msg_key, data)
  },
  failureResponse: async (res, next, msg_key, details, data) => {
    const response = new Response().bindContext(res);

    return response.failure(msg_key, details, data)
  },
  forwardFailureResponse: (res, next, statusCode, errorBody) => {
    const response = new Response().bindContext(res);
    
    return response.plain(errorBody, statusCode)
  },
  missingParamsResponse: async (res, next, msg_key, data) => {
    const response = new Response().bindContext(res);

    return response.failure(msg_key, undefined, data)
  }
}
