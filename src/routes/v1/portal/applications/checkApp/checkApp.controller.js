const logger = requireSrc('log')
const { getToken } = requireSrc('model').Token.GetToken;
const { findApplicationByNameAndOrganizationUuid } = requireSrc('model').Applications.GetApplication;
const { successResponse, failureResponse } = requireSrc('response').Handlers

const { validateInput } = require('./checkApp.validator')

module.exports = async (req, res, next) => {
  logger.debug('CHECK APPLICATION');
  try {
    // Validate inputs
    const data = validateInput(req);
      
    // Get access token from CA Portal
    const token = await getToken();
    
    // Check if the application exists or not
    const application = await findApplicationByNameAndOrganizationUuid({oauthToken: `${token.tokenType} ${token.accessToken}`, ...data});
    
    const isExist = application ? true : false;
    return successResponse(res, next, 'success', {isExist});

  } catch (error) {
    logger.error(`CHECK APPLICATION ERR: ${JSON.stringify(error)}`);
    return failureResponse(res, next, error.type || 'generic_server_error', error.details || 'Generic server side error');
  }    
}