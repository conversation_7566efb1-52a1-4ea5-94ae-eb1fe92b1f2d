const Validator = require('fastest-validator');
const { makeError } = requireSrc('util').CustomError;

module.exports = {
  validateInput: (req) => {
    const query = req.query;

    const validationSchema = {
        appName: {type: 'string', empty: false},
        orgUuid: {type: 'uuid', version: 4, empty: false}
    };

    const validator = new Validator();
    const validationResult = validator.validate(query, validationSchema);
    
    if(validationResult !== true) {
      let type = 'missing_param';
      if(validationResult[0].type !== 'required') {
        type = 'invalid_param';
      }
      throw makeError(type, validationResult[0].message);
    }
    
    return {...query};

    // Validator error check
    // if (validationResult !== true) {
    //   const errorMessage = validationResult[0].message
    //   if (validationResult[0].type === 'required') {
    //   logger.debug(`MISSING REQUIRED FIELD: ${errorMessage}`)
    //   return { error: { type: 'missing_param', details: errorMessage } }
    //   } else {
    //   logger.debug(`INVALID PARAM: ${errorMessage}`)
    //   return { error: { type: 'invalid_param', details: errorMessage } }
    //   }
    // }
  }
}