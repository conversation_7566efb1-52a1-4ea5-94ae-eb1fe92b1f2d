const logger = requireSrc('log')
const { getOrganizationByName } = requireSrc('model').Organizations.GetOrganization;
const { getToken } = requireSrc('model').Token.GetToken;
const { successResponse, failureResponse, missingParamsResponse, forwardFailureResponse } = requireSrc('response').Handlers

const { validateInput } = require('./checkOrg.validator')

module.exports = async (req, res, next) => {
  logger.debug('CHECK ORGANIZATION');
  try {
    // Validate inputs
    const data = validateInput(req);
    logger.debug(`INPUT PAYLOAD: ${JSON.stringify(data)}`);
    // Get access token from CA Portal
    const token = await getToken();

    // Get organization uuid if the org exists
    const organization = await getOrganizationByName({oauthToken: `${token.tokenType} ${token.accessToken}`, ...data});
    logger.debug(`RESPONSE ORGANIZATION: ${JSON.stringify(organization)}`);
    
    const response = organization ? {organizationUuid: organization.Uuid} : {};
    return successResponse(res, next, 'success', response);

  } catch (error) {
    logger.error(`CHECK ORGANIZATION ERR: ${JSON.stringify(error)}`);
    return failureResponse(res, next, error.type || 'generic_server_error', error.details || 'Generic server side error');
  }
}