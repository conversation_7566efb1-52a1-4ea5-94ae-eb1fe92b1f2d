const Validator = require('fastest-validator');
const { makeError } = requireSrc('util').CustomError;

module.exports = {
  validateInput: (req) => {
    const query = req.query;

    const validationSchema = {
        orgName: {type: 'string', empty: false},
    };

    const validator = new Validator();
    const validationResult = validator.validate(query, validationSchema);
    
    if(validationResult !== true) {
      let type = 'missing_param';
      if(validationResult[0].type !== 'required') {
        type = 'invalid_param';
      }
      throw makeError(type, validationResult[0].message);
    }

    // Check for cross env
    const suffix = req.headers && req.headers.scope && req.headers.scope.includes('cross_env') ? '-TEST' : '';
    query.orgName = decodeURI(query.orgName) + suffix;
    
    return {...query};
  }
}