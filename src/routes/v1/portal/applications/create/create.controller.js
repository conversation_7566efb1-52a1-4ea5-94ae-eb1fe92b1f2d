const logger = requireSrc('log')

const { successResponse, failureResponse, missingParamsResponse, forwardFailureResponse } = requireSrc('response').Handlers
const { lookupNonDeletedApplicationNameIfExist, countNonDeletedApplicationByOrganizationUuid, createOneApplicationRecord, removeOneApplicationRecordByUuid } = requireSrc('model').Applications.ApplicationInfoDB
const { addOneApplicationUsers, removeApplicationUserRecordByAppId } = requireSrc('model').Applications.ApplicationUsersDB
const { getApplicationsForOrganization, getCustomFieldsByName, createApplication, deleteApplication } = requireSrc('model').Apim
const { createMerchantAPI } = requireSrc('model').Merchants.MerchantsAPI
const { getTransferCoperate } = requireSrc('model').Transfer.TransferCoporateDB
const { insertMockAccounts } = requireSrc('model').Transfer.TransferAccountDB
const { writeSQS } = requireSrc('util').SQS

const { verifyOrgAppLimit, verifyAppLimit, validateApiGroupsUuid, generateCreateAppDBJSON, generateCreateAppCAJSON, createMockProfile, createMockMerchantProfile, generateMerchantsJSON, createConsentHTMLSQSPayload } = require('./create.methods')
const { APPLICATION_ICON } = require('./create.constants').customFields;
const { validateInput } = require('./create.validator')

module.exports = async (req, res, next) => {
  logger.info('CREATE APPLICATION')
  try {
    let { data, error } = validateInput(req)
    logger.debug(`validateInput error: ${error}`)
    if (error) {
      const errorMessage = {
        message: error.details
      }
      return missingParamsResponse(res, next, error.msg_key, errorMessage)
    }
    // await (createConsentHtmlAPI('72f00444-78f6-4a94-8bc2-f6482da8eba1'))
    // return successResponse(res, next, 'application_created', { 'message': 'ok' })
    const oauthToken = req.headers.authorization
    const organizationUuid = req.headers.organizationuuid
    const portalUserId = req.headers.useruuid
    const application = data.inputPayload.application
    const merchants = data.inputPayload.merchants
    const apiGroupIds = application.apiGroupIds
    const applicationName = application.applicationName

    // NOTE: Validate against this organization if exceeded max allowed app
    // 1. Validate base on applicationName existed that has status 'ENABLED' or 'DISABLED' from DB
    await lookupNonDeletedApplicationNameIfExist(applicationName, organizationUuid)
    // 1.1 Backward compability verify base on user app
    const applicationResults = await getApplicationsForOrganization({ organizationUuid })
    // 2. Validate base on current user organizationUuid from DB
    // Sample output {"numOfExistingApp": 1}
    const nonDeletedAppResult = await countNonDeletedApplicationByOrganizationUuid(organizationUuid)
    // NOTE: if num == 0, no application created yet under the organization in DB
    if (nonDeletedAppResult.numOfExistingApp > 0 || applicationResults != null) {
      // 2.1 Verify if number of app exceeded the limit specify in config
      await verifyAppLimit(nonDeletedAppResult.numOfExistingApp)
      // 2.2 Backward compability verify number of app per org
      await verifyOrgAppLimit(applicationResults, organizationUuid)
    }

    // 3. Validate if given UUID are valid to CA API Groups
    await validateApiGroupsUuid(apiGroupIds)
    
    // get custom fields from CA
    const requiredCustomFields = [APPLICATION_ICON]
    const customFields = await getCustomFieldsByName({ Name: requiredCustomFields })
    if (!customFields || Object.keys(customFields).length !== requiredCustomFields.length) {
      logger.debug(`REQUIRED CUSTOM FIELDS: ${JSON.stringify(requiredCustomFields)}`)
      logger.error(`One or more CustomField not found.`)
      throw { type: 'generic_server_error' }
    }
    // 4. Generate/Populate payload for Create application via CA Portal API
    const createAppPayload = await generateCreateAppCAJSON(application, customFields, organizationUuid)
    const applicationUuid = createAppPayload.Uuid

    // 4.1. Send payload to create application via CA APIs
    const createAppResponse = await createApplication(createAppPayload)
    // 5. Generate/Populate payload for Create application to DB, currently only support SANDBOX
    const createApplicationDBPayload = await generateCreateAppDBJSON(createAppPayload, createAppResponse, 'SANDBOX')
    // 5.1 Create new application record in DB
    await createOneApplicationRecord(createApplicationDBPayload)
    await addOneApplicationUsers(applicationUuid, portalUserId)

    // 7. Format and send payload to create merchants
    const merchantsArray = {
      merchants: await generateMerchantsJSON(merchants, applicationUuid)
    }

    const createMerchantResponse = await createMerchantAPI(merchantsArray)
    logger.debug(`createMerchantResponse: ${JSON.stringify(createMerchantResponse)}`)

    // 8. Validate the output from merchant API response
    if (createMerchantResponse.statusCode >= 400) {
      // NOTE: If create merchant fails, delete applications
      const deletePayload = { uuid: applicationUuid }
      logger.debug(`deletePayload: ${JSON.stringify(deletePayload)}`)
      await deleteApplication(deletePayload)
      await removeOneApplicationRecordByUuid(applicationUuid)
      await removeApplicationUserRecordByAppId(applicationUuid)
      // Throw as error to response to user
      throw createMerchantResponse
    } else {
      // Create basic mock profiles for application
      createMockProfile(applicationUuid, portalUserId)
      // Create basic mock merchant profiles for application
      createMockMerchantProfile(applicationUuid, portalUserId)
      // Create mock account
      const transferCoperateResult = await getTransferCoperate()
      const randomIndex = Math.floor(Math.random() * transferCoperateResult.length);
      const randomCorporateId = transferCoperateResult[randomIndex].corporateId;
      await insertMockAccounts(randomCorporateId,applicationUuid)
      // Create sandbox createConsentHtmlAPI
      const writeSQSPayload = await createConsentHTMLSQSPayload(createAppPayload.ApiKey)
      writeSQS(writeSQSPayload, 'Create Consent HTML Sandbox')

      const responseResult = {
        applicationUuid: applicationUuid,
        status: createAppResponse.Status
      }

      return successResponse(res, next, 'application_created', responseResult)
    }
  } catch (error) {
    logger.error(`CREATE APPLICATION ERR: ${error}`)
    logger.error(`CREATE APPLICATION ERR: ${JSON.stringify(error)}`)
    if (error.hasOwnProperty('type')) {
      return failureResponse(res, next, error.type, { error })
    } else if (error.hasOwnProperty('statusCode')) {
      // NOTE: Assuming this is come from upstream response error
      logger.debug(`error.statusCode: ${error.statusCode}`)
      logger.debug(`error.body: ${JSON.stringify(error.body)}`)
      return forwardFailureResponse(res, next, error.statusCode, error.body)
    } else {
      return failureResponse(res, next, 'generic_server_error', { error })
    }
  }
}
