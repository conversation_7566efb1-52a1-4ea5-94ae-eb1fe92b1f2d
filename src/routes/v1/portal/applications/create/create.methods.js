const logger = requireSrc('log')
const uuidv4 = require('uuid/v4')
const dayjs = require('dayjs')
const _ = require('lodash');
const { maxAppAllowed, microServiceSBApiBasePath, microServiceSBCertPath } = requireSrc('config')
const { getApiGroups } = requireSrc('model').Apim
const { createOneCustomerProfileAPI } = requireSrc('model').MockCustomerProfile.MockCustomerProfileAPI
const { createOneMerchantProfileAPI } = requireSrc('model').MockMerchantProfile.MockMerchantProfileAPI
const pathRegex = /^http[s]*:\/\/|\/.*$/g
const hostHeader = microServiceSBApiBasePath.replace(pathRegex, '')
const { APPLICATION_ICON } = require('./create.constants').customFields;

module.exports = {
  /**
   * [Verify if a organization has exceeded the numbers of App]
   * @param  {[type]} applicationList  [Array of application]
   * @param  {[type]} organizationUuid [organizationUuid in String]
   * @return {[type]}                  [description]
   */
  verifyOrgAppLimit: (applicationList, organizationUuid) => {
    logger.info(`verifyOrgAppLimitation()`)
    return new Promise((resolve, reject) => {
      // NOTE: Number of max app per organization
      const maxApp = maxAppAllowed
      logger.debug(`DEBUG applicationList: ${JSON.stringify(applicationList)}`)
      logger.debug(`DEBUG organizationUuid: ${organizationUuid}`)
      const result = applicationList ? applicationList.filter(result => result.OrganizationUuid === organizationUuid) : []

      if (result.length >= maxApp) {
        const failureObject = {
          type: 'reached_maximum_app'
        }
        reject(failureObject)
      } else {
        resolve()
      }
    }) // End Promise()
  }, // End verifyOrgAppLimit()
  /**
   * Verify if an application has exceeded number of application by limit
   * @param  {[type]} numOfExistingApp [Number]
   * @return {[type]}                  [description]
   */
  verifyAppLimit: (numOfExistingApp) => {
    logger.info(`verifyAppLimit()`)
    logger.debug(`numOfExistingApp: ${numOfExistingApp}`)
    return new Promise((resolve, reject) => {
      // NOTE: Number of max app per organization
      const maxApp = maxAppAllowed
      if (numOfExistingApp >= maxApp) {
        const failureObject = {
          type: 'reached_maximum_app'
        }
        reject(failureObject)
      } else {
        resolve()
      }
    }) // End Promise()
  }, // End verifyAppLimit()
  /**
   * This will generate JSON Payload to create DB record for an application
   * @param  {[type]} createAppPayload  [description]
   * @param  {[type]} createAppResponse [description]
   * @param  {[type]} environmentType   [description]
   * @return {[type]}                   [description]
   */
  generateCreateAppDBJSON: (createAppPayload, createAppResponse, environmentType) => {
    logger.info(`generateCreateAppDBJSON()`)
    return new Promise((resolve, reject) => {
      try {
        const epochTimestamp = dayjs().unix(Number)

        const toReturn = {
          applicationUuid: createAppPayload.Uuid,
          applicationName: createAppPayload.Name,
          description: createAppPayload.Description,
          apiKey: createAppPayload.ApiKey,
          keySecret: createAppPayload.KeySecret,
          callbackUrl: createAppPayload.OauthCallbackUrl,
          organizationUuid: createAppPayload.OrganizationUuid,
          status: createAppResponse.Status,
          environment: environmentType,
          created_at: epochTimestamp,
          updated_at: epochTimestamp,
          deleted_at: null
        }
        logger.debug(`toReturn: ${JSON.stringify(toReturn)}`)

        resolve(toReturn)
      } catch (error) {
        logger.error(`generateCreateAppDBJSON() error - ${error}`)
        reject(error)
      }
    }) // End Promise()
  }, // generateCreateAppDBJSON()
  /**
   * [This will generate JSON Payload to create Application via CA Portal API]
   * @param  {[type]} applicationInputPayload [description]
   * @return {[type]}                         [description]
   */
  generateCreateAppCAJSON: (applicationInputPayload, customFields, organizationUuid) => {
    logger.info(`generateCreateAppCAJSON()`)
    return new Promise((resolve, reject) => {
      try {
        const apiKey = `l7${uuidv4().replace(/-/g, '')}`
        const apiSecret = uuidv4().replace(/-/g, '')
        const applicationUuid = uuidv4()
        const magMasterKey = uuidv4()
        // NOTE: Default scopes
        const defaultScopes = `login profiles-sb deeplink-sb transactions-sb transactions.retrieval transactions.cancel profiles.cid profiles.name profiles.birthdate profiles.gender profiles.address profiles.mobile profiles.email profiles.country payment-sb loanorigination-sb profiles.cardtype maemanee.merchant.profile-sb maemanee.payment.qr.create-sb maemanee.payment.paymentlink-sb maemanee.payment.transaction.getlist-sb maemanee.payment.transaction.getone-sb`

        // NOTE: Temp solution
        const oauthType = ['none', 'public', 'confidential']
        const description = applicationInputPayload.description || ''
        const callbackUrl = applicationInputPayload.callbackUrl || 'yourapp://'

        const toReturn = {
          Uuid: applicationUuid,
          Name: applicationInputPayload.applicationName,
          OrganizationUuid: organizationUuid,
          Description: description,
          ApiKey: apiKey,
          KeySecret: apiSecret,
          OauthScope: defaultScopes,
          OauthCallbackUrl: callbackUrl,
          MagMasterKey: magMasterKey,
          MagScope: '',
          OauthType: oauthType[2],
          CustomFieldValues: {
            results: [{
              Uuid: uuidv4(),
              CustomFieldUuid: customFields[APPLICATION_ICON],
              EntityUuid: uuidv4(),
              Value: '',
            }]
          },
          ApiIds: {
            results: []
          },
          ApiGroupIds: {
            results: applicationInputPayload.apiGroupIds
          }
        }
        logger.debug(`toReturn: ${JSON.stringify(toReturn)}`)

        resolve(toReturn)
      } catch (error) {
        logger.error(`generateCreateAppCAJSON() error - ${error}`)
        reject(error)
      }
    }) // End Promise()
  }, // End generateCreateAppCAJSON()
  /**
   * Generate merchants payload with applicationUuid into each merchant object in array
   * @param  {[type]} merchantsPayload [merchants JSON Objects]
   * @param  {[type]} applicationUuid  [UUID of application Uuid]
   * @return {[type]}                  [JSON Object]
   */
  generateMerchantsJSON: (merchantsPayload, applicationUuid) => {
    logger.info(`generateMerchantsJSON()`)
    return new Promise(async (resolve, reject) => {
      try {
        const createMerchantPayload = []
        for (let eachMerchant of merchantsPayload) {
          eachMerchant.portal_applicationId = applicationUuid

          // Mapping request for LEGO
          eachMerchant.merchantLegoId = _.random(**********, **********).toString()
          eachMerchant.merchantLegoSubAccountId = _.random(************, ************).toString()
          eachMerchant.merchantLegoAccountNo = _.random(************, ************).toString()
          eachMerchant.merchantLegoBalance = 0

          createMerchantPayload.push(eachMerchant)
        }
        logger.debug(`createMerchantPayload: ${JSON.stringify(createMerchantPayload)}`)
        resolve(createMerchantPayload)
      } catch (error) {
        logger.error(`generateMerchantsJSON() error - ${error}`)
        reject(error)
      }
    }) // End Promise()
  }, // End generateMerchantsJSON()
  /**
   * Use this to create mock profiles
   * @param  {[type]} profileTypeArray [description]
   * @return {[type]}                  [description]
   */
  createMockProfile: (portal_applicationId, portal_userId) => {
    logger.info(`createMockProfile()`)
    return new Promise(async (resolve, reject) => {
      try {
        // Change from 1 of each type, to citizen ID x3 profile
        var profileTypeArray = ['P1', 'P1', 'P1']
        for (let i = 0; i <= (profileTypeArray.length - 1); i++) {
          logger.debug(`profileTypeArray: ${profileTypeArray[i]}`)

          const createProfilePayload = {
            applicationUuid: portal_applicationId,
            cardType: profileTypeArray[i]
          }
          await createOneCustomerProfileAPI(createProfilePayload, portal_userId)
        }
        resolve()
      } catch (error) {
        logger.error(`createMockProfile() error - ${error}`)
        reject(error)
      }
    }) // End Promise()
  }, // End createMockProfile()
  /**
   * Use this to create mock merchant profiles
   * @param  {[type]} portal_applicationId [portal application uuid]
   * @param  {[type]} portal_userId        [portal useruuid]
   * @return {[type]}                      [description]
   */
   createMockMerchantProfile: (portal_applicationId, portal_userId) => {
    logger.info(`createMockProfile()`)
    return new Promise(async (resolve, reject) => {
      try {
        const createProfilePayload = {
          applicationUuid: portal_applicationId,
          shopTypeCode: 'MT03'
        }
        await createOneMerchantProfileAPI(createProfilePayload, portal_userId)
        resolve()
      } catch (error) {
        logger.error(`createMockMerchantProfile() error - ${error}`)
        reject(error)
      }
    }) // End Promise()
  }, // End createMockMerchantProfile()
  /**
   * [Validate each apiGroupIds UUID are a valid UUID on CA]
   * @param  {[type]} oauthToken     [CA Portal OAuth Token]
   * @param  {[type]} apiGroupsArray [apiGroupsList/Array]
   * @return {[type]}                [description]
   */
  validateApiGroupsUuid: (apiGroupsArray) => {
    logger.info(`validateApiGroupsUuid()`)
    return new Promise(async (resolve, reject) => {
      try {
        const groups = await getApiGroups()
        const apiGroupIds = groups.map(g => g.uuid);
        if((_.difference(apiGroupsArray, apiGroupIds)).length > 0) {
          return reject({ type: 'generic_server_error', details: 'One or more api-group is invalid' })
        } else {
          return resolve()
        }
      } catch (error) {
        logger.error(`validateApiGroupsUuid() error - ${error}`)
        reject(error)
      }
    }) // End Promise()
  }, // End validateApiGroupsUuid()
  /**
   * Create JSON Payload for generating Consent HTML via SQS Payload
   * To be formatted as of how request payload like
   * @param  {[type]} applicationApiKey [api key of the application]
   * @return {[type]}                   [description]
   */
  createConsentHTMLSQSPayload: (applicationApiKey) => {
    logger.info(`createConsentHTMLSQSPayload()`)
    return new Promise(async (resolve, reject) => {
      try {
        const url = `${microServiceSBApiBasePath}sandbox/v1/consent/generator`
        const payload = {
          'applicationId': [applicationApiKey]
        }

        const consentHTMLPayload = {
          url,
          method: `POST`,
          headers: {
            'Content-Type': 'application/json;charset=utf-8',
            'accept-language': 'en',
            'host': hostHeader
          },
          json: true,
          body: payload
        }
        logger.debug(`consentHTMLPayload: ${JSON.stringify(consentHTMLPayload)}`)
        resolve(consentHTMLPayload)
      } catch (error) {
        logger.error(`createConsentHTMLSQSPayload() error - ${error}`)
        reject(error)
      }
    }) // End Promise()
  } // End createConsentHTMLSQSPayload()
}
