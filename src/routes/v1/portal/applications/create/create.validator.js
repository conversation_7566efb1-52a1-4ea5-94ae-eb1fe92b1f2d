const logger = requireSrc('log')
const _ = require('lodash')
let Validator = require('fastest-validator')

module.exports = {
  validateInput: (req) => {
    const payload = req.body
    // const englishThaiCharacterRegExp = new RegExp('^[^-\s][a-z A-Z\u0E00-\u0E7F]+$')

    let v = new Validator()
    let applicationSchema = {
      application: {
        type: 'object',
        props: {
          applicationName: { type: 'string', empty: false, min: 1, max: 50 },
          description: { type: 'string', empty: true, max: 255 },
          apiGroupIds: { type: 'array', empty: false, min: 1, items: { type: 'uuid', version: 4, empty: false } },
          callbackUrl: { type: 'string', empty: true, max: 255 }
          // customDescriptionEN: { type: 'string', empty: true, max: 255 },
          // customDescriptionTH: { type: 'string', empty: true, max: 255 },
        }
      }
    }

    let applicationValidatorResult = v.validate(payload, applicationSchema)

    // Validator function checker
    if (applicationValidatorResult !== true) {
      const errorMessage = applicationValidatorResult[0].message
      if (applicationValidatorResult[0].type === 'required') {
        logger.debug(`MISSING REQUIRED FIELD: ${errorMessage}`)
        return { error: { msg_key: 'missing_param', details: errorMessage } }
      } else {
        logger.debug(`INVALID PARAM: ${errorMessage}`)
        return { error: { msg_key: 'invalid_param', details: errorMessage } }
      }
    }

    // Validate ApplicatinName will not contains double space
    const regex = /[\s]{2,}|[\t\r\n]+|^\s+|\s+$/ // Must not contain double spaces
    if (regex.test(payload.application.applicationName)) {
      logger.debug(`INVALID PARAM: applicationName contains invalid character`)
      return { error: { msg_key: 'invalid_param', details: 'applicationName should not contains double spaces and not allow to start/end with space' } }
    }

    // Validate callbackPayment and callbackRegister start with "http://" or "https://"
    const merchantPayload = payload.merchants;
    const pathRegex = /^https?:\/\//;
    for (let i = 0; i < merchantPayload.length; i++) {
      if ((merchantPayload[i].callbackPayment && !pathRegex.test(merchantPayload[i].callbackPayment))
      || (merchantPayload[i].callbackRegister && !pathRegex.test(merchantPayload[i].callbackRegister))) {
        return {
          error: {
            msg_key: "generic_server_error",
            details: "Generic server side error",
          }
        };
      }
    }

    // NOTE: Validate if apiGroupIds each value is an valid UUID
    const applicationPayload = payload.application
    if (applicationPayload && applicationPayload.hasOwnProperty('apiGroupIds')) {
      // for (let eachApiGroup of applicationPayload.apiGroupIds) {
      //   logger.debug(`eachGroup: ${eachApiGroup}`)
      //   if (!uuidValidator(eachApiGroup, 4)) {
      //     logger.debug(`apiGroup of uuid: ${eachApiGroup} is invalid UUID v4 format`)
      //     return { error: { msg_key: 'invalid_param', details: 'apiGroupIds contains invalid UUID' } }
      //   }
      // }
      // NOTE: Validate if there any duplicate UUID
      if (_.uniq(applicationPayload.apiGroupIds).length !== applicationPayload.apiGroupIds.length) {
        return { error: { msg_key: 'invalid_param', details: 'apiGroupIds contains duplicate UUID' } }
      }
    }

    // NOTE: For now it only allow 1 merchant and 1 notification object in array
    let merchantsSchema = {
      merchants: { type: 'array',
        min: 1,
        max: 1,
        items: {
          type: 'object',
          props: {
            merchantName: { type: 'string', empty: true, min: 0, max: 25 },
            billerName: { type: 'string', empty: true, min: 0, max: 25 },
            referenceType: { type: 'enum', empty: false, values: ['1', '2'] },
            requiredCvv: { type: 'boolean', empty: false },
            merchantLegoName: { type: 'string', empty: true, min: 0, max: 25 },
            callbackRegister: { type: 'string', empty: true, optional: true, min: 0, max: 255 },
            callbackPayment: { type: 'string', empty: true,optional: true, min: 0, max: 255 },
            notifications: { type: 'array',
              min: 1,
              max: 1,
              items: {
                type: 'object',
                props: {
                  callbackUrl: { type: 'string', empty: true },
                  default: { type: 'boolean', empty: false }
                }
              }
            }
          }
        }
      }
    }

    let merchantsValidatorResult = v.validate(payload, merchantsSchema)

    // Validator function checker
    if (merchantsValidatorResult !== true) {
      const errorMessage = merchantsValidatorResult[0].message
      if (merchantsValidatorResult[0].type === 'required') {
        logger.debug(`MISSING REQUIRED FIELD: ${errorMessage}`)
        return { error: { msg_key: 'missing_param', details: errorMessage } }
      } else {
        logger.debug(`INVALID PARAM: ${errorMessage}`)
        return { error: { msg_key: 'invalid_param', details: errorMessage } }
      }
    }

    return { data: { inputPayload: payload } }
  }
}
