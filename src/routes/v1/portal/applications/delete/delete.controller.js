const logger = requireSrc('log')

const { successResponse, failureResponse, missingParamsResponse } = requireSrc('response').Handlers
const { deleteOneMerchantAPI, getOneMerchantAPI } = requireSrc('model').Merchants.MerchantsAPI
const { updateOneDeletedApplicationUserByAppId } = requireSrc('model').Applications.ApplicationUsersDB
const { updateOneDeletedApplicationInfoByUuid } = requireSrc('model').Applications.ApplicationInfoDB
const { getApplication, deleteApplication } = requireSrc('model').Apim
const { writeSQS } = requireSrc('util').SQS
const { getAccountByApplicationUuid,deleteAccountByAccountUuid } = requireSrc('model').Transfer.TransferAccountDB
const { deleteActivityAccountByAccount } = requireSrc('model').Transfer.TransferAccountActivityDB
const { deleteBlacklistAccountByAccount } = requireSrc('model').Transfer.TransferBlacklistAccountDB
const { deleteConsentHTMLSQSPayload } = require('./delete.methods')

const { getCustomerProfilesByAppIdAPI, deleteOneMockCustomerProfileByBankRmidAPI } = requireSrc('model').MockCustomerProfile.MockCustomerProfileAPI
const { deleteAllMockMerchantProfileByApplicationUuidAPI } = requireSrc('model').MockMerchantProfile.MockMerchantProfileAPI
const { validateInput } = require('./delete.validator.js')

module.exports = async (req, res, next) => {
  logger.debug('DELETE APPLICATION')
  try {
    let { data, error } = validateInput(req)
    if (error) {
      const errorMessage = {
        message: error.details
      }
      return missingParamsResponse(res, next, error.msg_key, errorMessage)
    }
    const applicationUuid = data.inputParams.application_uuid
    const oauthToken = req.headers.authorization
    const portalUserId = req.headers.useruuid

    const payload = { uuid: applicationUuid }

    // 1. Get the list of application by this user OAuth
    const applicationResponse = await getApplication(payload)

    // 2. Delete applications by CA Portal APIs
    await deleteApplication(payload)

    // Delete all mock merchants profile belong to this portal uuid
    await deleteAllMockMerchantProfileByApplicationUuidAPI(applicationUuid, portalUserId)

    // 3. Update field for DB applications, application_users
    // Update the deleted_at timestamp and set status to DISABLED etc
    await updateOneDeletedApplicationInfoByUuid(applicationUuid)
    await updateOneDeletedApplicationUserByAppId(applicationUuid)
    const accountList = (await getAccountByApplicationUuid(applicationUuid)).map(item => item.account);
    await deleteActivityAccountByAccount(accountList)
    await deleteBlacklistAccountByAccount(accountList)
    await deleteAccountByAccountUuid(applicationUuid)
    
    // 4. Retrieve list of merchants to this portal uuid
    const merchants = await getOneMerchantAPI(applicationUuid)
    // 4.1 If merchants array > 0, start deleting each of the merchants
    if (merchants.length > 0) {
      for (let eachMerchant of merchants) {
        // Deleting of merchants
        await deleteOneMerchantAPI(eachMerchant.merchantId)
      }
    }

    // 5. Retrieve list of mock customers profile belong to this portal uuid
    const mockCustomerProfiles = await getCustomerProfilesByAppIdAPI(applicationUuid)
    logger.debug(`mockCustomerProfiles: ${JSON.stringify(mockCustomerProfiles)}`)
    logger.debug(`mockCustomerProfiles.profiles.length: ${mockCustomerProfiles.profiles.length}`)
    // 5.1 If customer profiles array > 0, start deleting
    if (mockCustomerProfiles.profiles.length > 0) {
      for (let eachCustomerProfile of mockCustomerProfiles.profiles) {
        // Deleting of customer profiles
        await deleteOneMockCustomerProfileByBankRmidAPI(eachCustomerProfile.bankRmid, portalUserId)
      }
    }

    // 6. Generate sandbox deleteConsentHTML SQS Payload
    const writeSQSPayload = await deleteConsentHTMLSQSPayload(applicationResponse.ApiKey)
    // 6.1 Send payload to SQS
    writeSQS(writeSQSPayload, 'Delete Consent HTML Sandbox')
    let toReturn = {
      message: 'Successfully deleted'
    }

    return successResponse(res, next, 'success', toReturn)
  } catch (error) {
    logger.error(`DELETE APPLICATIONS ERR: ${JSON.stringify(error)}`)
    if (error.hasOwnProperty('type')) {
      return failureResponse(res, next, error.type, { error })
    } else {
      return failureResponse(res, next, 'generic_server_error', { error })
    }
  }
}
