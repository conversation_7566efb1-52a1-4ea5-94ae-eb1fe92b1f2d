const logger = requireSrc('log')
const { microServiceSBApiBasePath, microServiceSBCertPath } = requireSrc('config')
const pathRegex = /^http[s]*:\/\/|\/.*$/g
const hostHeader = microServiceSBApiBasePath.replace(pathRegex, '')

module.exports = {
  /**
   * Create JSON Payload for deleting Consent HTML via SQS Payload
   * To be formatted as of how request payload like
   * @param  {[type]} applicationApiKey [api key of the application]
   * @return {[type]}                   [description]
   */
  deleteConsentHTMLSQSPayload: (applicationApiKey) => {
    logger.info(`deleteConsentHTMLSQSPayload()`)
    return new Promise(async (resolve, reject) => {
      try {
        const url = `${microServiceSBApiBasePath}sandbox/v1/consent/generator`
        const payload = {
          'applicationId': applicationApiKey
        }

        const consentHTMLPayload = {
          url,
          method: `DELETE`,
          headers: {
            'Content-Type': 'application/json;charset=utf-8',
            'accept-language': 'en',
            'host': hostHeader
          },
          json: true,
          body: payload
        }
        logger.debug(`consentHTMLPayload: ${JSON.stringify(consentHTMLPayload)}`)
        resolve(consentHTMLPayload)
      } catch (error) {
        logger.error(`deleteConsentHTMLSQSPayload() error - ${error}`)
        reject(error)
      }
    }) // End Promise()
  } // End deleteConsentHTMLSQSPayload()
}
