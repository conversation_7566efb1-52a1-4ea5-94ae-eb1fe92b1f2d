const logger = requireSrc('log')

module.exports = {
  validateInput: (req) => {
    const params = req.params

    if (params.application_uuid === null || params.application_uuid === undefined || params.application_uuid === '') {
      logger.debug(`APPLICATIONS_UUID IS MISSING FROM QUERY PARAMS`)
      return { error: { msg_key: 'missing_param', details: 'application_uuid is required in query parameters'}}
    }

    return { data: { inputParams: params } }
  }
}
