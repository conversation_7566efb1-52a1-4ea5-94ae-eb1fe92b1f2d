const { validateInput } = require('./getAppInfo.validator');
const logger = requireSrc('log');
const { getAppInfo } = require('./getAppInfo.methods');

const { successResponse, failureResponse } =
  requireSrc('response').Handlers;

module.exports = async (req, res, next) => {
  logger.info('GET APP INFO');
  try {
    const { data } = validateInput(req);
    const { identifyValue } = data;
    const result = await getAppInfo({ ApiKey: identifyValue });
    return successResponse(res, next, 'success', result);
  } catch (error) {
    logger.error(`GET ONE APPLICATION ERR: ${JSON.stringify(error)}`);
    if (error.hasOwnProperty('type')) {
      return failureResponse(res, next, error.type, { error });
    } else {
      return failureResponse(res, next, 'generic_server_error', { error });
    }
  }
};
