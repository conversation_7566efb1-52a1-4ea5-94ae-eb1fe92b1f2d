const logger = requireSrc('log');
const { searchApplication } = require('../../../../../model/apim');
const { find, get } = require('lodash');
const { makeError } = requireSrc('util').CustomError;

function mapResponse(caResponse) {
  const { Uuid, OrganizationName, Name, CustomFieldValues } = caResponse;
  return {
    portalApplicationUuid: Uuid,
    organizationName: OrganizationName,
    applicationName: Name,
    certificateUuid: get(
      find(CustomFieldValues.results, ['Name', 'TwoWaySslMapping']),
      'Value',
      '' // default value instead of accessing object.undefined
    ),
  };
}

async function getAppInfo(payload) {
  logger.info('getAppInfo');
  const response = await searchApplication(payload);
  logger.debug('getAppInfo: response', response);
  if (Array.isArray(response) && response.length > 0) {
    logger.info('getAppInfo: found result');
    return mapResponse(response[0]);
  }
  logger.info(`getAppInfo: result not found`);
  throw makeError('not_found', 'result not found');
}

module.exports = {
  getAppInfo,
};
