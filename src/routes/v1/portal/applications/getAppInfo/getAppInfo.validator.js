const Validator = require('fastest-validator');
const { pick } = require('lodash');
const logger = requireSrc('log');
const { makeError } = requireSrc('util').CustomError;

const validator = new Validator();

const IDENTIFY_TYPE = {
  APIKEY: 'APIKEY',
};

module.exports = {
  validateInput: (req) => {
    logger.info('validateInput');
    const { query } = req;
    const queryValidationSchema = {
      identifyType: { type: 'enum', values: [IDENTIFY_TYPE.APIKEY] },
      identifyValue: { type: 'string', empty: false ,pattern : "^l7([0-9a-z]+([a-z]+[0-9a-z]+)+)$" },
      $$strict: 'remove',
    };
    const validationResult = validator.validate(
      { ...query },
      { ...queryValidationSchema }
    );
    if (validationResult !== true) {
      let type = 'missing_param';
      if (validationResult[0].type !== 'required') {
        type = 'invalid_param';
      }
      throw makeError(type, validationResult[0].message);
    }

    // When identifyType === 'APIKEY' --> identifyValue must starts with 'l7'
    if (
      query.identifyType === IDENTIFY_TYPE.APIKEY &&
      !query.identifyValue.startsWith('l7')
    ) {
      throw makeError('invalid_param', "The identifyValue must starts with 'l7'");
    }

    return {
      data: {
        ...pick(query, ['identifyType', 'identifyValue']),
      },
    };
  },
};
