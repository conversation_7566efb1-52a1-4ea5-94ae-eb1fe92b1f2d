const logger = requireSrc('log')

const { successResponse, failureResponse, missingParamsResponse } = requireSrc('response').Handlers

const { lookupApplicationUuidByStatus, createOneApplicationRecord } = requireSrc('model').Applications.ApplicationInfoDB
const { addOneApplicationUsers } = requireSrc('model').Applications.ApplicationUsersDB
const { getApplication } = requireSrc('model').Apim
const { getOneMerchantAPI } = requireSrc('model').Merchants.MerchantsAPI
const { getAppIconURL } = requireSrc('model').Uploads.UploadsAPI

const { formatApplicationResponse, generatePatchingAppDBJSON } = require('./getOne.methods')

// const { validateInput } = require('./getOne.validator')

module.exports = async (req, res, next) => {
  logger.debug('GET ONE APPLICATION')
  try {
    const oauthToken = req.headers.authorization
    const organizationUuid = req.headers.organizationuuid
    const portalUserId = req.headers.useruuid
    const applicationUuid = req.params.application_uuid

    // 1. Get the application from ca using orgUuid and appUuid
    const applicationResponse = await getApplication({ uuid: applicationUuid })

    // 1.1 Backward compatibility fixing where data does not exist in DB
    // NOTE: Optional start populating fields
    const lookupApplicationResponse = await lookupApplicationUuidByStatus(applicationUuid, applicationResponse.Status)
    logger.debug(`lookupApplicationResponse: ${JSON.stringify(lookupApplicationResponse)}`)
    if (lookupApplicationResponse.length === 0) {
      // NOTE: If CA Portal APIs return record, but DB does not exist
      // Start patching data
      const applicationPatchingDB = await generatePatchingAppDBJSON(applicationResponse)
      await createOneApplicationRecord(applicationPatchingDB)
      await addOneApplicationUsers(applicationUuid, portalUserId)
    }
    // 2. Get merchant info, if merchant not found, it will return as empty array []
    const merchants = await getOneMerchantAPI(applicationUuid)
    
    // 2.1 Get merchant LEGO info from first merchant info, if merchant LEGO not found, it will return as {}
    let merchantLegos = {}
    if (merchants && merchants[0]) {
      merchantLegos = merchants[0].merchantLegos
    }
    for (let merchant of merchants) {
      delete merchant.merchantLegos
    }

    // 3. Get application icon self-sign URL if any
    // If application icon not found, it will return as empty string
    const appIconUrl = await getAppIconURL(applicationUuid)

    // 4. Clean up data before returning
    const toReturn = {
      application: await formatApplicationResponse(applicationResponse),
      merchants,
      merchantLegos
    }
    toReturn.application.applicationIcon = appIconUrl

    return successResponse(res, next, 'success', toReturn)
  } catch (error) {
    logger.error(`GET ONE APPLICATION ERR: ${JSON.stringify(error)}`)
    if (error.hasOwnProperty('type')) {
      return failureResponse(res, next, error.type, { error })
    } else {
      return failureResponse(res, next, 'generic_server_error', { error })
    }
  }
}
