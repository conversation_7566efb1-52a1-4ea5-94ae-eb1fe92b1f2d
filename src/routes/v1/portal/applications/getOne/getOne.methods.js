const logger = requireSrc('log')
const dayjs = require('dayjs')
module.exports = {
  /**
   * Format incoming payload to be in camel case before returning
   * @param  {[type]} applicationJSON [description]
   * @return {[type]}                 [description]
   */
  formatApplicationResponse: (applicationJSON) => {
    logger.info(`verifyOrgAppLimitation()`)
    return new Promise((resolve, reject) => {
      // NOTE: Return format are to be in camel case, CA format are all Uppercase
      try {
        const toReturn = {
          applicationUuid: applicationJSON.Uuid,
          applicationName: applicationJSON.Name,
          description: applicationJSON.Description,
          apiKey: applicationJSON.ApiKey,
          keySecret: applicationJSON.KeySecret,
          callbackUrl: applicationJSON.OauthCallbackUrl,
          organizationUuid: applicationJSON.OrganizationUuid,
          organizationName: applicationJSON.OrganizationName,
          apiGroupIds: applicationJSON.ApiGroupIds,
          customFieldValues: applicationJSON.CustomFieldValues,
          status: applicationJSON.Status
          // disabledByType: applicationJSON.DisabledByType,
          // magMasterKey: applicationJSON.MagMasterKey,
          // oauthType: applicationJSON.OauthType,
          // reason: applicationJSON.Reason,
          // oauthScope: applicationJSON.OauthScope,
          // magScope: applicationJSON.MagScope,
          // apiIds: applicationJSON.ApiIds
        }
        resolve(toReturn)
      } catch (error) {
        logger.error(`formatApplicationResponse() error - ${error}`)
        reject(error)
      }
    }) // End Promise()
  }, // End formatApplicationResponse()
  /**
   * Use this method to generate payload to create DB structure to patch missing data
   * @param  {[type]} appPayload [description]
   * @return {[type]}            [description]
   */
  generatePatchingAppDBJSON: (appPayload) => {
    logger.info(`generatePatchingAppDBJSON()`)
    return new Promise((resolve, reject) => {
      try {
        const epochTimestamp = dayjs().unix(Number)

        const toReturn = {
          applicationUuid: appPayload.Uuid,
          applicationName: appPayload.Name,
          description: appPayload.Description,
          apiKey: appPayload.ApiKey,
          keySecret: appPayload.KeySecret,
          callbackUrl: appPayload.OauthCallbackUrl,
          organizationUuid: appPayload.OrganizationUuid,
          status: appPayload.Status,
          created_at: epochTimestamp,
          updated_at: epochTimestamp,
          deleted_at: null
        }
        logger.debug(`toReturn: ${JSON.stringify(toReturn)}`)

        resolve(toReturn)
      } catch (error) {
        logger.error(`generatePatchingAppDBJSON() error - ${error}`)
        reject(error)
      }
    }) // End Promise()
  } // generatePatchingAppDBJSON()
}
