const logger = requireSrc('log')
const { getOrganizationByName } = requireSrc('model').Organizations.GetOrganization;
const { findApplicationByNameAndOrganizationUuid } = requireSrc('model').Applications.GetApplication;
const { createApplication } = requireSrc('model').Applications.CreateApplication;
const { getAccountPlanByName } = requireSrc('model').AccountPlans.GetAccountPlan;
const { getCustomFieldsByName } = requireSrc('model').CustomFields.GetCustomField;
const { createScope } = requireSrc('model').Scopes.CreateScope;
const { deleteScope } = requireSrc('model').Scopes.DeleteScope;
const { getToken } = requireSrc('model').Token.GetToken;
const { successResponse, failureResponse } = requireSrc('response').Handlers;
const { defaultAccountPlan } = requireSrc('config');
const { generateCreateOrganizationPayload, generateCreateScopePayload, generateCreateApplicationPayload } = require('./create.methods')
const { validateInput } = require('./create.validator');
const { APPLICATION_NAME, CALLBACK_URL, BUSINESS_UNIT, ORGANIZATION_NAME, TWO_WAY_SSL_MAPPING, APPLICATION_CODE, APPLICATION_CODE_NSS, PRODUCT_NAME_NSS } = require('./create.constants').customFields;
const { makeError } = requireSrc('util').CustomError;
const { postOrganization } = requireSrc('model').Apim

module.exports = async (req, res, next) => {
  logger.debug('CREATE APPLICATION LIVE');
  let newScopeUuid = undefined;
  try {
    // Validate data
    const data = validateInput(req);
    
    // Get access token from CA Portal
    const token = await getToken();
    
    // Get Organization from CA.
    let organization = await getOrganizationByName({oauthToken: `${token.tokenType} ${token.accessToken}`, orgName: data.organizationName});
    
    // If organization exist, check for duplicate application name. Otherwise create a new one
    if (organization) {
      // Organization already exists
      data.organizationUuid = organization.Uuid;

      // Check if application already exists or not
      let application = await findApplicationByNameAndOrganizationUuid({oauthToken: `${token.tokenType} ${token.accessToken}`, appName: data.applicationName, orgUuid: data.organizationUuid});
      if (application) {
        throw makeError('duplicate_application_name', 'Application name already exists');
      }
    }
    else {
      // Organization doesn't exist
      // Get account plan from CA Portal
      const accountPlan = await getAccountPlanByName({oauthToken: `${token.tokenType} ${token.accessToken}`, accountPlanName: defaultAccountPlan});
      if(!accountPlan) {
        logger.error(`ACCOUNT PLAN MISSING: '${defaultAccountPlan}' does not exist`);
        throw makeError();
      }
      
      // Get Organization Payload
      const organizationPayload = generateCreateOrganizationPayload({...data, accountPlanName: accountPlan.Name, accountPlanUuid: accountPlan.Uuid});
      
      // Create new organization in CA Portal
      organization  = await postOrganization(organizationPayload);
      data.organizationUuid = organization.Uuid;
    }

    // Get custom field (businessUnit) uuid from CA Portal
    const fieldNames = [APPLICATION_NAME, CALLBACK_URL, BUSINESS_UNIT, ORGANIZATION_NAME, TWO_WAY_SSL_MAPPING, APPLICATION_CODE, APPLICATION_CODE_NSS, PRODUCT_NAME_NSS];
    const customFields = await getCustomFieldsByName({oauthToken: `${token.tokenType} ${token.accessToken}`, fieldNames});
    if (!customFields || Object.keys(customFields).length !== fieldNames.length) {
      logger.debug(`REQUIRED CUSTOM FIELDS: ${JSON.stringify(fieldNames)}`);
      logger.debug(`EXISTING CUSTOM FIELDS: ${JSON.stringify(customFields)}`);
      logger.error(`CUSTOM FIELD MISSING: One or more of the custom fields does not exist`);
      throw makeError();
    }

    // Get Scope payload
    const scopePayload = generateCreateScopePayload(data);

    // Create new scope in CA Portal and get the scopeUuid
    const scope = await createScope(scopePayload);
    newScopeUuid = scope.scopeUuid;
    
    // Get Application Payload
    const applicationPayload = generateCreateApplicationPayload({...data, ...scope, customFields});
    
    // Create new application in CA Portal
    application = await createApplication(`${token.tokenType} ${token.accessToken}`, applicationPayload);

    // Response Payload
    const response = {
      portalOrganizationUuid: data.organizationUuid,
      portalOrganizationName: data.organizationName,

      portalApplicationUuid: application.Uuid,
      portalApplicationName: application.Name,

      portalApiKey: application.ApiKey,
      portalApiSecret: application.KeySecret,
    }

    return successResponse(res, next, 'success', response)

  } catch (error) {
    const errorText = JSON.stringify(error);
    logger.error(`CREATE APPLICATION ERR: ${errorText !== '{}' ? errorText : error}`);

    // Delete scope if created
    if(newScopeUuid) {
      await deleteScope({scopeUuid: newScopeUuid});
    }

    return failureResponse(res, next, error.type || 'generic_server_error', error.details || 'Generic server side error');
  }
}
