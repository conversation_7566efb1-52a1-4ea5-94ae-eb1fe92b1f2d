const uuidv4 = require('uuid/v4')
const { defaultApiUuid } = requireSrc('config');
const { customFields: fieldNames } = require('./create.constants');
const getType = (applicationType) => {
  switch(applicationType) {
    case 'mobile_app':
      return 'mobile';
    case 'desktop_web':
      return 'server';
    case 'embedded_web':
    case 'mobile_web':
      return 'frontend';
    default:
      return '';
  }
};

module.exports = {
  generateCreateOrganizationPayload: (data) => {
    const orgUuid = uuidv4();

    return {
      Uuid: orgUuid,
      Description: '',
      AccountPlanName: data.accountPlanName,
      ApplicationUsage: '0',
      Name: data.organizationName,
      Status: 'ENABLED',
      AccountPlanUuid: data.accountPlanUuid,
      PrivateApiUsage: '0',
      UserUsage: '0',
      PrivateAppUsage: '0'
    }
  },

  generateCreateScopePayload: (data) => {
    const scopeUuid = uuidv4();

    return {
      scopeUuid: scopeUuid,
      scopeList: data.scopeList
    }
  },

  generateCreateApplicationPayload: (data) => {
    const appUuid = uuidv4();
    const apiKey = `l7${uuidv4().replace(/-/g, '')}`;
    const apiSecret = uuidv4().replace(/-/g, '');
    const magMasterKey = uuidv4();
    const { customFields, applicationUrl, businessUnit, applicationName, engApplicationName, thaiApplicationName, engOrganizationName, thaiOrganizationName, applicationType, twoWaySslMapping, applicationCode, applicationCodeNss, productNameNss } = data;

    const type = getType(applicationType);
    const callbackUrl = applicationType !== 'server_to_server' ? JSON.stringify({ [type]: { default: applicationUrl } }) : '';
    
    const fieldsToSave = [
      {
        Uuid: uuidv4(),
        CustomFieldUuid: customFields[fieldNames.APPLICATION_NAME],
        EntityUuid: uuidv4(),
        Value: JSON.stringify({en: engApplicationName, th: thaiApplicationName})
      },
      {
        Uuid: uuidv4(),
        CustomFieldUuid: customFields[fieldNames.BUSINESS_UNIT],
        EntityUuid: uuidv4(),
        Value: businessUnit
      },
      {
        Uuid: uuidv4(),
        CustomFieldUuid: customFields[fieldNames.ORGANIZATION_NAME],
        EntityUuid: uuidv4(),
        Value: JSON.stringify({en: engOrganizationName, th: thaiOrganizationName})
      },
      {
        Uuid: uuidv4(),
        CustomFieldUuid: customFields[fieldNames.TWO_WAY_SSL_MAPPING],
        EntityUuid: uuidv4(),
        Value: twoWaySslMapping || ''
      },
      {
        Uuid: uuidv4(),
        CustomFieldUuid: customFields[fieldNames.APPLICATION_CODE],
        EntityUuid: uuidv4(),
        Value: applicationCode || ''
      },
      {
        Uuid: uuidv4(),
        CustomFieldUuid: customFields[fieldNames.APPLICATION_CODE_NSS],
        EntityUuid: uuidv4(),
        Value: applicationCodeNss || ''
      },
      {
        Uuid: uuidv4(),
        CustomFieldUuid: customFields[fieldNames.PRODUCT_NAME_NSS],
        EntityUuid: uuidv4(),
        Value: productNameNss ? JSON.stringify({ 'products': productNameNss }) : ''
      },
      {
        Uuid: uuidv4(),
        CustomFieldUuid: customFields[fieldNames.CALLBACK_URL],
        EntityUuid: uuidv4(),
        Value: callbackUrl
      },
    ];

    const payload = {
      Uuid: appUuid,
      Name: applicationName,
      OrganizationUuid: data.organizationUuid,
      Description: data.description || '',
      OauthCallbackUrl: applicationUrl || '',
      ApiKey: apiKey,
      KeySecret: apiSecret,
      OauthScope: data.scopeUuid,
      MagMasterKey: magMasterKey,
      MagScope: '',
      OauthType: 'confidential',
      ApiIds: {
        results: [defaultApiUuid]
      },
      CustomFieldValues: {
        results: fieldsToSave
      }
    }

    return payload;
  }
}
