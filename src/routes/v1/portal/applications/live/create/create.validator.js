const Validator = require('fastest-validator');
const _ = require('lodash');
const { makeError } = requireSrc('util').CustomError;

module.exports = {
  validateInput: (req) => {
    const body = req.body;

    if (!body) {
      throw makeError('missing_param', 'Request body is missing')
    }
    
    // convert authenticationType to array
    if (body.authenticationType) {
      try {
        body.authenticationType = JSON.parse(body.authenticationType);
        // check productNameNss is array
        if (!Array.isArray(body.authenticationType)) {
          throw makeError('invalid_param', 'authenticationType field is invalid format');
        }
      } catch (err) {
        throw makeError('invalid_param', 'authenticationType field is invalid format');
      }
    }

    // convert productNameNss to array of
    if (body.productNameNss) {
      try {
        body.productNameNss = JSON.parse(body.productNameNss);
        // check productNameNss is array
        if (!Array.isArray(body.productNameNss)) {
          throw makeError('invalid_param', 'productNameNss field is invalid format');
        }
      } catch (err) {
        throw makeError('invalid_param', 'productNameNss field is invalid format');
      }
    }

    const validationSchema = {
      systemOrganizationName: { type: 'string', empty: false },
      engOrganizationName: { type: 'string', empty: false, max: 45 },
      thaiOrganizationName: { type: 'string', empty: false },
      applicationName: { type: 'string', empty: false, max: 50 },
      engApplicationName: { type: 'string', empty: false },
      thaiApplicationName: { type: 'string', empty: false },
      applicationUrl: { type: 'string', empty: false },
      applicationType: { type: 'string', enum: ['mobile_app', 'mobile_web', 'embedded_web', 'desktop_web', 'server_to_server'] },
      scopeList: { type: 'string', empty: false },
      businessUnit: { type: 'string', empty: false },
      twoWaySslMapping: { type: 'uuid', optional: true },
      applicationCode: { type: 'string', empty: false, max: 15 },
      authenticationType: { type: 'array', items: { type: 'enum', values: ['2-legged', '3-legged', 'hybrid', 'web-authen'] }, empty: false },
      applicationCodeNss: { type: 'string', empty: false , max: 15 },
      productNameNss: { type: 'array', items: { type: 'string', empty: false }, unique: true }
    };

    if(body.applicationType === 'server_to_server') {
      validationSchema.applicationUrl.optional = true;
    }

    const realtimePaymentScopes = [
      'payment.transfer.debit.initiate',
      'payment.transfer.credit.initiate',
      'payment.transfer.confirm',
      'payment.transfer.inquiry'
    ];
    const payloadScopes = body.scopeList.split(' ');
    if((_.intersection(payloadScopes, realtimePaymentScopes)).length === 0) {
      validationSchema.applicationCode.optional = true;
    }
    
    if (!body.authenticationType.includes('web-authen')) {
      validationSchema.applicationCodeNss.optional = true;
      validationSchema.productNameNss.optional = true;
    }

    const validator = new Validator();
    const validationResult = validator.validate(body, validationSchema);

    if(validationResult !== true) {
      let type = 'missing_param';
      if(validationResult[0].type !== 'required') {
        type = 'invalid_param';
      }
      throw makeError(type, validationResult[0].message);
    }

    // Update org name based on cross env
    const suffix = req.headers && req.headers.scope && req.headers.scope.includes('cross_env') ? '-TEST' : '';
    body.organizationName = body.systemOrganizationName + suffix;
    
    return {...body};
  }
}
