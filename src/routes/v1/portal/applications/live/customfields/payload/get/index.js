const logger = requireSrc('log');
const { getS3File } = requireSrc('util/s3');
const { successResponse, failureResponse } = requireSrc('response').Handlers;

module.exports = async (req, res, next) => {
  logger.debug('GET CUSTOMFIELD PAYLOAD');
  try {
    // get payload from S3
    const s3Payload = await getS3File();
    
    return successResponse(res, next, 'success', s3Payload);

  } catch (error) {
    const errorText = JSON.stringify(error);
    logger.error(`GET CUSTOMFIELD PAYLOAD ERR: ${errorText !== '{}' ? errorText : error}`);
    return failureResponse(res, next, error.type || 'generic_server_error', error.details || 'Generic server side error');
  }
}