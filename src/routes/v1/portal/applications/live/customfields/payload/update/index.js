const logger = requireSrc('log');
const { updateS3File } = requireSrc('util/s3');
const { makeError } = requireSrc('util').CustomError;
const { validateInput } = require('./validator');
const { successResponse, failureResponse } = requireSrc('response').Handlers;

module.exports = async (req, res, next) => {
  logger.debug('UPDATE CUSTOMFIELD PAYLOAD');
  try {
    const data = validateInput(req);
    
    await updateS3File(data);

    return successResponse(res, next, 'success');
  } catch (error) {
    const errorText = JSON.stringify(error);
    logger.error(`UPDATE CUSTOMFIELD PAYLOAD ERR: ${errorText !== '{}' ? errorText : error}`);
    return failureResponse(res, next, error.type || 'generic_server_error', error.details || 'Generic server side error');
  }
}