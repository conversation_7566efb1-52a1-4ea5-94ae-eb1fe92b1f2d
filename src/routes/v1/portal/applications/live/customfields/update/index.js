const _ = require('lodash');
const logger = requireSrc('log');
const { searchApplication, getCustomFieldsByName, promisifyUpdateApplicaion } = requireSrc('model').Apim;
const { getS3File } = requireSrc('util/s3');
const { successResponse, failureResponse } = requireSrc('response').Handlers;
const { generateApplicationCustomFields, generatePayloadCustomFieldNames, generateUpdateApplicationPayload, mergeCustomFields } = require('./methods');
const { makeError } = requireSrc('util').CustomError;

module.exports = async (req, res, next) => {
  logger.debug('UPDATE APPLICTION CUSTOMFIELD');
  try {
    // get payload from S3
    const s3Payload = await getS3File();
    
    // Get applications
    const apiKeys = Object.keys(s3Payload);
    const applications = await searchApplication({ ApiKey: apiKeys });
    if(apiKeys.length !== applications.length) throw makeError('invalid_param', 'One or more ApiKeys in the payload is invalid');

    // Find out the list of common custom fields that exist for each applocation
    const applicationCustomFields = generateApplicationCustomFields(applications);

    // Find out the list of common custom fields from the payload
    const payloadCustomFieldNames = generatePayloadCustomFieldNames(s3Payload);

    // Check for any new custom field in the payload that is required for that applications
    const applicationCustomFieldNames = Object.keys(applicationCustomFields);
    const differentCustomFieldNames = _.difference(payloadCustomFieldNames, applicationCustomFieldNames);

    // Get the list of new custom fields (if there is any)
    if(differentCustomFieldNames.length > 0) {
      const caCustomFields = await getCustomFieldsByName({ Name: differentCustomFieldNames });
      if(differentCustomFieldNames.length !== Object.keys(caCustomFields).length) {
        logger.debug(`REQUIRED CUSTOM FIELDS: ${JSON.stringify(differentCustomFieldNames)}`);
        throw makeError('invalid_param', 'One or more of the custom fields does not exist');
      }
      mergeCustomFields(applicationCustomFields, caCustomFields);
    }
    
    // Generate payload. Payload will update the list of original applications that are retriened from CA
    generateUpdateApplicationPayload(applications, s3Payload, applicationCustomFields);

    const promises = [];
    applications.forEach(app => {
      promises.push( promisifyUpdateApplicaion(app) );
    });

    await Promise.all(promises);
    return successResponse(res, next, 'success');

  } catch (error) {
    const errorText = JSON.stringify(error);
    logger.error(`UPDATE APPLICATION CUSTOMFIELD ERR: ${errorText !== '{}' ? errorText : error}`);
    return failureResponse(res, next, error.type || 'generic_server_error', error.details || 'Generic server side error');
  }
}