const _ = require('lodash');
const uuidv4 = require('uuid/v4');
const generateApplicationCustomFields = (applications) => {
  const customFields = {};
  applications.forEach(app => {
    app.CustomFieldValues.results.forEach(field => {
      customFields[field.Name] = field.CustomFieldUuid;
    });
  });

  return customFields;
};

const generatePayloadCustomFieldNames = (payload) => {
  let customFields = [];
  Object.values(payload).forEach(item => {
    customFields = customFields.concat(Object.keys(item));
  });

  return _.uniq(customFields);
};

const mergeCustomFields = (oldFields, newFields) => {
  // oldFields and newFields are objects { "FieldName": "FieldUuid" }
  Object.assign(oldFields, newFields);
};

const generateUpdateApplicationPayload = (applications, payload, appCustomFields) => {
  applications.forEach(app => {
    // Get object from payload to update for an application
    const fieldsToUpdate = payload[app.ApiKey];
    const customFieldNames = Object.keys(fieldsToUpdate);
    customFieldNames.forEach(name => {
      let customField = app.CustomFieldValues.results.find(field => field.Name === name);
      if(!customField) {
        customField = {
          Uuid: uuidv4(),
          CustomFieldUuid: appCustomFields[name],
          EntityUuid: uuidv4()
        };
        app.CustomFieldValues.results.push(customField);
      }
      customField.Value = fieldsToUpdate[name];
    });
  });
  return applications;
};
module.exports = {
  generateApplicationCustomFields,
  generatePayloadCustomFieldNames,
  mergeCustomFields,
  generateUpdateApplicationPayload,
}
