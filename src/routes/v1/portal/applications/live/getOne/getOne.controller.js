const logger = requireSrc('log')
const { getToken } = requireSrc('model').Token.GetToken;
const { findApplicationByUuid } = requireSrc('model').Applications.GetApplication;
const { getOrganizationByUuid } = requireSrc('model').Organizations.GetOrganization;
const { getScopeByUuid } = requireSrc('model').Scopes.GetScope;
const { successResponse, failureResponse } = requireSrc('response').Handlers
const { makeError } = requireSrc('util').CustomError;

const { validateInput } = require('./getOne.validator')

module.exports = async (req, res, next) => {
  logger.debug('GET ONE LIVE APPLICATION')
  try {
    // Validate Input
    const data = validateInput(req);
    
    // Get access token
    const token = await getToken();
    
    // Get application
    const application = await findApplicationByUuid({oauthToken: `${token.tokenType} ${token.accessToken}`, ...data});
    
    // Get organization
    const organization = await getOrganizationByUuid({oauthToken: `${token.tokenType} ${token.accessToken}`, organizationUuid: application.OrganizationUuid});
    
    if(organization.AccountPlanName !== 'ClientService') {
      //return failureResponse(res, next, 'invalid_application', 'Invalid application');
      throw makeError('invalid_application', 'Invalid application');
    }

    // Scope object
    let scope = { scopeUuid: '', scopeList: application.OauthScope };

    // validate if the application.OauthScope is text or UUID
    const uuidPattern = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-5][0-9a-f]{3}-[089ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if(uuidPattern.test(application.OauthScope)) {
      // application.OauthScope is a UUID, so get scope from CA
      scope = await getScopeByUuid({scopeUuid: application.OauthScope});
    }
    
    // Response payload
    const responsePayload = {
      applicationUuid: application.Uuid,
      applicationName: application.Name,
      scopeUuid: scope.scopeUuid,
      scopeList: scope.scopeList,
      organizationUuid: organization.Uuid,
      organizationName: organization.Name,
    };
    
    return successResponse(res, next, 'success', responsePayload);

  } catch (error) {
    logger.error(`GET ONE LIVE APPLICATION ERR: ${JSON.stringify(error)}`)
    return failureResponse(res, next, error.type || 'generic_server_error', error.details || 'Generic server side error');
  }
}
