const logger = requireSrc('log');
const { findApplicationByUuid } = requireSrc('model').Applications.GetApplication;
const { updateApplication } = requireSrc('model').Applications.UpdateApplication;
const { getCustomFieldsByName } = requireSrc('model').CustomFields.GetCustomField;
const { getScopeByUuid } = requireSrc('model').Scopes.GetScope;
const { updateScope } = requireSrc('model').Scopes.UpdateScope;
const { getToken } = requireSrc('model').Token.GetToken;
const { successResponse, failureResponse } = requireSrc('response').Handlers;
const { generateUpdateApplicationPayload } = require('./update.methods');
const { validateInput } = require('./update.validator');
const { APPLICATION_NAME, CALLBACK_URL, APPLICATION_CODE, APPLICATION_CODE_NSS, PRODUCT_NAME_NSS } = require('./update.constants').customFields;
const { makeError } = requireSrc('util').CustomError;

module.exports = async (req, res, next) => {
  logger.debug('UPDATE APPLICTION LIVE');
  let oldScope = undefined;
  try {
    // Validate data
    const data = validateInput(req);
    
    // Get access token from CA Portal
    const token = await getToken();

    // Get application
    const application = await findApplicationByUuid({oauthToken: `${token.tokenType} ${token.accessToken}`, portalApplicationUuid: data.applicationUuid});

    // Get scope
    const scope = await getScopeByUuid({scopeUuid: application.OauthScope});

    // Update scope if changed
    if(scope.scopeList !== data.scopeList) {
      await updateScope({scopeUuid: scope.scopeUuid, scopeList: data.scopeList});
      oldScope = scope;
    }

    // Get custom fields
    const fieldNames = [APPLICATION_NAME, CALLBACK_URL, APPLICATION_CODE, APPLICATION_CODE_NSS, PRODUCT_NAME_NSS];
    const customFields = await getCustomFieldsByName({oauthToken: `${token.tokenType} ${token.accessToken}`, fieldNames});
    
    if (!customFields || Object.keys(customFields).length !== fieldNames.length) {
      logger.info(`REQUIRED CUSTOM FIELDS: ${JSON.stringify(fieldNames)}`);
      logger.info(`EXISTING CUSTOM FIELDS: ${JSON.stringify(customFields)}`);
      logger.error(`CUSTOM FIELD MISSING: One or more of the custom fields does not exist`);
      throw makeError();
    }

    // Update application
    const updateApplicationPayload = generateUpdateApplicationPayload(application, data, customFields);

    await updateApplication(updateApplicationPayload, `${token.tokenType} ${token.accessToken}`);

    return successResponse(res, next, 'success', data);

  } catch (error) {
    const errorText = JSON.stringify(error);
    logger.error(`UPDATE APPLICATION ERR: ${errorText !== '{}' ? errorText : error}`);

    // Rollback scope if updated
    if(oldScope) {
      await updateScope(oldScope);
    }

    return failureResponse(res, next, error.type || 'generic_server_error', error.details || 'Generic server side error');
  }
}