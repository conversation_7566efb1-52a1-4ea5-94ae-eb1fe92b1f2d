const uuidv4 = require('uuid/v4');
const { customFields: fieldNames } = require('./update.constants');
const getType = (applicationType) => {
  switch(applicationType) {
    case 'mobile_app':
      return 'mobile';
    case 'desktop_web':
      return 'server';
    case 'embedded_web':
    case 'mobile_web':
      return 'frontend';
    default:
      return '';
  }
};
module.exports = {
  generateUpdateApplicationPayload: (application, data, customFields) => {
    const type = getType(data.applicationType);

    // Required fields when create application. So as of now, no need to check for null.
    // In future, should check for null if the create application validation logic changes to optional
    const customFieldAppName = application.CustomFieldValues.results.find(item => item.Name === fieldNames.APPLICATION_NAME);
    customFieldAppName.Value = JSON.stringify({ en: data.engApplicationName, th: data.thaiApplicationName });

    // Optional fields when create application. So as of now, should check for null.
    // In future, no need to check for null if the create application validation logic changes to required
    
    // ApplicationCode
    let customFieldAppCode = application.CustomFieldValues.results.find(item => item.Name === fieldNames.APPLICATION_CODE);
    if(!customFieldAppCode) {
      customFieldAppCode = {
        Uuid: uuidv4(),
        CustomFieldUuid: customFields[fieldNames.APPLICATION_CODE],
        EntityUuid: uuidv4(),
      };
      // add new custom field
      application.CustomFieldValues.results.push(customFieldAppCode);
    }
    customFieldAppCode.Value = data.applicationCode || '';
    
    // CallbackUrls
    let customFieldAppUrl = application.CustomFieldValues.results.find(item => item.Name === fieldNames.CALLBACK_URL);
    if(!customFieldAppUrl) {
      customFieldAppUrl = {
        Uuid: uuidv4(),
        CustomFieldUuid: customFields[fieldNames.CALLBACK_URL],
        EntityUuid: uuidv4(),
      };
      // add new custom field
      application.CustomFieldValues.results.push(customFieldAppUrl);
    }
    //ApplicationCodeNss
    let customFieldAppCodeNss = application.CustomFieldValues.results.find(item => item.Name === fieldNames.APPLICATION_CODE_NSS);
    if(!customFieldAppCodeNss) {
      customFieldAppCodeNss = {
        Uuid: uuidv4(),
        CustomFieldUuid: customFields[fieldNames.APPLICATION_CODE_NSS],
        EntityUuid: uuidv4(),
      };
      // add new custom field
      application.CustomFieldValues.results.push(customFieldAppCodeNss);
    }
    customFieldAppCodeNss.Value = data.applicationCodeNss || '';

    //ProductNameNss
    let customFieldProductNameNss = application.CustomFieldValues.results.find(item => item.Name === fieldNames.PRODUCT_NAME_NSS);
    if(!customFieldProductNameNss) {
      customFieldProductNameNss = {
        Uuid: uuidv4(),
        CustomFieldUuid: customFields[fieldNames.PRODUCT_NAME_NSS],
        EntityUuid: uuidv4(),
      };
      // add new custom field
      application.CustomFieldValues.results.push(customFieldProductNameNss);
    }
    customFieldProductNameNss.Value = data.productNameNss ? JSON.stringify({ 'products': data.productNameNss }) : '';
    
    customFieldAppUrl.Value = data.applicationType !== 'server_to_server' ? JSON.stringify({ [type]: { default: data.applicationUrl } }) : '';
    
    application.OauthCallbackUrl = data.applicationType !== 'server_to_server' ? data.applicationUrl : '';
    
    return application;
  } 
}
