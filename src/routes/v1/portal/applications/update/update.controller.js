const logger = requireSrc('log')

const { successResponse, failureResponse, missingParamsResponse, forwardFailureResponse } = requireSrc('response').Handlers

const { updateOneApplicationInfoByUuid } = requireSrc('model').Applications.ApplicationInfoDB
const { getApplication, updateApplication } = requireSrc('model').Apim
const { getOneMerchantAPI, updateOneMerchantAPI, createMerchantAPI } = requireSrc('model').Merchants.MerchantsAPI
const { writeSQS } = requireSrc('util').SQS
const { APPLICATION_ICON } = require('./update.constants').customFields

const { filterUpdateApplicationObject, generatePostMerchantsJSON, generateUpdateAppDBJSON, updateConsentHTMLSQSPayload, generateCustomFieldsValues } = require('./update.methods')

const { validateInput, validateNewMerchantsInput, validateExistingMerchantsInput } = require('./update.validator')

module.exports = async (req, res, next) => {
  logger.info('UPDATE APPLICATION UUID')
  try {
    let { data, error } = validateInput(req)

    if (error) {
      const errorMessage = {
        message: error.details
      }
      return missingParamsResponse(res, next, error.msg_key, errorMessage)
    }
    const userInputApplication = data.inputPayload.application
    const applicationUuid = req.params.application_uuid
    // const portalUserId = req.headers.useruuid || req.headers.userUuid
    const oauthToken = req.headers.authorization
    let userInputMerchants = {}

    // 1. Retrieve User's Application by Application's Uuid, ensure the application exist
    const applicationObject = await getApplication({ uuid: applicationUuid })

    logger.debug(`userInputApplication: ${JSON.stringify(userInputApplication)}`)

    // 2. Replace values if input by user
    // NOTE: Replaced each value by user input, if not exist, used back current applicationObject values

    if (userInputApplication.applicationName !== '') { applicationObject.Name = userInputApplication.applicationName }
    applicationObject.OauthCallbackUrl = userInputApplication.callbackUrl || 'yourapp://'
    applicationObject.Description = userInputApplication.description || ''

    // 2.1 For upload applicationIcon on ca portal 
    if (userInputApplication.applicationIcon && userInputApplication.applicationIcon !== '' ) {
      const fieldNames = [APPLICATION_ICON]
      const s3BucketKey = userInputApplication.applicationIcon.key || ''
      applicationObject.CustomFieldValues = await generateCustomFieldsValues(fieldNames, s3BucketKey, applicationObject.CustomFieldValues)
    }

    // 3. Retrieve merchants information, it may be empty array, created on the old application flow
    const merchantsObject = await getOneMerchantAPI(applicationUuid)
    logger.debug(`merchantsObject: ${JSON.stringify(merchantsObject)}`)

    // NOTE: Base on merchantsObject, do difference validation check on payload
    if (merchantsObject.length > 0) {
      let { data, error } = validateExistingMerchantsInput(req)
      if (error) {
        const errorMessage = {
          message: error.details
        }
        return missingParamsResponse(res, next, error.msg_key, errorMessage)
      }
      userInputMerchants = data.inputPayload.merchants
    } else {
      let { data, error } = validateNewMerchantsInput(req)
      if (error) {
        const errorMessage = {
          message: error.details
        }
        return missingParamsResponse(res, next, error.msg_key, errorMessage)
      }
      userInputMerchants = data.inputPayload.merchants
    }
    logger.debug(`userInputMerchants: ${JSON.stringify(userInputMerchants)}`)

    // 4. Remove any non-required JSON key/pair
    const updateApplicationPayload = await filterUpdateApplicationObject(applicationObject)
    logger.debug(`updateApplicationPayload: ${JSON.stringify(updateApplicationPayload)}`)

    // 5. Update Application via CA Portal API
    await updateApplication(updateApplicationPayload)

    // 6. Update DB of new values
    const updateAppDBJSON = await generateUpdateAppDBJSON(updateApplicationPayload)
    await updateOneApplicationInfoByUuid(updateAppDBJSON)

    // 7. Update merchants records, get records first, than compare object
    // NOTE: Logic to check if merchants exist or not
    if (merchantsObject.length > 0) {
      // 7.1 Do PUT merchants for update
      for (let eachMerchant of userInputMerchants) {
        logger.debug(`eachMerchant: ${JSON.stringify(eachMerchant)}`)
        const merchantId = eachMerchant.merchantId
        delete eachMerchant.merchantId

        const updateMerchantResponse = await updateOneMerchantAPI(merchantId, eachMerchant)
        logger.debug(`updateMerchantResponse: ${JSON.stringify(updateMerchantResponse)}`)
        if (updateMerchantResponse.statusCode >= 400) {
          throw updateMerchantResponse
        }
      }
    } else {
      // Create merchant, backward support where applications does not have merchants
      // Do POST merchants for create
      // 7.2. Send payload to create merchants
      const merchantsArray = {
        merchants: await generatePostMerchantsJSON(userInputMerchants, updateApplicationPayload.Uuid)
      }
      const createMerchantResponse = await createMerchantAPI(merchantsArray)
      logger.debug(`createMerchantResponse: ${JSON.stringify(createMerchantResponse)}`)
      if (createMerchantResponse.statusCode >= 400) {
        // Throw as error to response to user
        throw createMerchantResponse
      }
    }

    // 8. Generate sandbox deleteConsentHTML SQS Payload
    const writeSQSPayload = await updateConsentHTMLSQSPayload(applicationObject.ApiKey)
    // 8.1 Send payload to SQS
    writeSQS(writeSQSPayload, 'Delete Consent HTML Sandbox')

    return successResponse(res, next, 'success')
  } catch (error) {
    logger.error(`UPDATE APPLICATIONS ERR: ${error}`)
    logger.error(`UPDATE APPLICATIONS ERR: ${JSON.stringify(error)}`)
    if (error.hasOwnProperty('type')) {
      return failureResponse(res, next, error.type, { error })
    } else if (error.hasOwnProperty('statusCode')) {
      // NOTE: Assuming this is come from upstream response error
      logger.debug(`error.statusCode: ${error.statusCode}`)
      logger.debug(`error.body: ${JSON.stringify(error.body)}`)
      return forwardFailureResponse(res, next, error.statusCode, error.body)
    } else {
      return failureResponse(res, next, 'generic_server_error', { error })
    }
  }
}

