const logger = requireSrc('log')
const dayjs = require('dayjs')
const _ = require('lodash')
const { microServiceSBApiBasePath, microServiceSBCertPath } = requireSrc('config')
const pathRegex = /^http[s]*:\/\/|\/.*$/g
const hostHeader = microServiceSBApiBasePath.replace(pathRegex, '')
const { APPLICATION_ICON } = require('./update.constants').customFields
const { getCustomFieldsByName } = requireSrc('model').Apim
const uuidv4 = require('uuid/v4')

module.exports = {
  /**
   * Use this method to filter out required payload for update applications only
   * @param  {[type]} unfilteredApplicationObject [Original CA API application response payload from get one application]
   * @return {[type]}                             [description]
   */
  filterUpdateApplicationObject: (unfilteredApplicationObject) => {
    logger.info(`filterUpdateApplicationObject()`)
    return new Promise((resolve, reject) => {
      try {
        logger.debug(`unfilteredApplicationObject: ${JSON.stringify(unfilteredApplicationObject)}`)

        const updateApplicationModel = {
          Uuid: null,
          Name: null,
          OrganizationUuid: null,
          OrganizationName: null,
          Description: null,
          Status: null,
          DisabledByType: null,
          OauthScope: null,
          OauthCallbackUrl: null,
          ApiKey: null,
          KeySecret: null,
          MagMasterKey: null,
          MagScope: null,
          OauthType: null,
          Reason: null,
          ApiIds: null,
          ApiGroupIds: null,
          CustomFieldValues: null
        }
        const keys = _.keys(updateApplicationModel)
        const toReturn = _.pick(unfilteredApplicationObject, keys)
        logger.debug(`toReturn: ${JSON.stringify(toReturn)}`)
        resolve(toReturn)
      } catch (error) {
        logger.error(`filterUpdateApplicationObject() error - ${error}`)
        reject(error)
      }
    }) // End Promise()
  }, // End filterUpdateApplicationObject()
  filterUpdateMerchantObject: (unfilteredMerchantObject) => {
    logger.info(`filterUpdateMerchantObject()`)
    return new Promise((resolve, reject) => {
      try {
        logger.debug(`unfilteredMerchantObject: ${JSON.stringify(unfilteredMerchantObject)}`)

        const updateApplicationModel = {
          Uuid: null,
          Name: null,
          OrganizationUuid: null,
          OrganizationName: null,
          Description: null,
          Status: null,
          DisabledByType: null,
          OauthScope: null,
          OauthCallbackUrl: null,
          ApiKey: null,
          KeySecret: null,
          MagMasterKey: null,
          MagScope: null,
          OauthType: null,
          Reason: null,
          ApiIds: null,
          ApiGroupIds: null,
          CustomFieldValues: null
        }
        const payload = JSON.parse(unfilteredMerchantObject)
        const keys = _.keys(updateApplicationModel)
        const toReturn = _.pick(payload, keys)
        logger.debug(`toReturn: ${JSON.stringify(toReturn)}`)
        resolve(toReturn)
      } catch (error) {
        logger.error(`filterUpdateApplicationObject() error - ${error}`)
        reject(error)
      }
    }) // End Promise()
  }, // End filterUpdateMerchantObject()
  /**
   * Generate merchants payload with applicationUuid into each merchant object in array
   * @param  {[type]} merchantsPayload [merchants JSON Objects]
   * @param  {[type]} applicationUuid  [UUID of application Uuid]
   * @return {[type]}                  [JSON Object]
   */
  generatePostMerchantsJSON: (merchantsPayload, applicationUuid) => {
    logger.info(`generateMerchantsJSON()`)
    return new Promise(async (resolve, reject) => {
      try {
        const createMerchantPayload = []
        for (let eachMerchant of merchantsPayload) {
          eachMerchant.portal_applicationId = applicationUuid
          createMerchantPayload.push(eachMerchant)
        }
        logger.debug(`createMerchantPayload: ${JSON.stringify(createMerchantPayload)}`)
        resolve(createMerchantPayload)
      } catch (error) {
        logger.error(`createMockProfile() error - ${error}`)
        reject(error)
      }
    }) // End Promise()
  }, // End generateMerchantsJSON()
  /**
   * This will generate JSON Payload to create DB record for an application
   * @param  {[type]} createAppPayload  [description]
   * @param  {[type]} createAppResponse [description]
   * @return {[type]}                   [description]
   */
  generateUpdateAppDBJSON: (applicationObject) => {
    logger.info(`generateUpdateAppDBJSON()`)
    return new Promise((resolve, reject) => {
      try {
        const epochTimestamp = dayjs().unix(Number)

        const toReturn = {
          applicationUuid: applicationObject.Uuid,
          applicationName: applicationObject.Name,
          description: applicationObject.Description,
          apiKey: applicationObject.ApiKey,
          keySecret: applicationObject.KeySecret,
          callbackUrl: applicationObject.OauthCallbackUrl,
          organizationUuid: applicationObject.OrganizationUuid,
          status: applicationObject.Status,
          updated_at: epochTimestamp,
          deleted_at: null
        }
        logger.debug(`toReturn: ${JSON.stringify(toReturn)}`)

        resolve(toReturn)
      } catch (error) {
        logger.error(`generateUpdateAppDBJSON() error - ${error}`)
        reject(error)
      }
    }) // End Promise()
  }, // generateUpdateAppDBJSON()
  /**
   * Search for Custom Field object and replace it values and return as whole CustomFieldValues JSON Object
   * @param  {[type]} customObjectArray [Custom JSON Object Array]
   * @param  {[type]} name              [Name of the Custom Object eg. ApplicationIcon]
   * @param  {[type]} value             [value to be replaced with]
   * @return {[type]}                   [Promise]
   */
  replaceCustomObject: (customObjectArray, name, value) => {
    logger.info(`replaceCustomObject()`)
    return new Promise((resolve, reject) => {
      const result = customObjectArray.find(result => result.Name === name)

      if (!result) {
        logger.error(`No Such CustomFieldValues Name`)
        reject()
      } else {
        result.Value = value
        customObjectArray[name] = result
        const toReturn = { results: customObjectArray }
        resolve(toReturn)
      }
    }) // End Promise()
  }, // replaceCustomObject();
  /**
   * Create JSON Payload for generating Consent HTML via SQS Payload
   * To be formatted as of how request payload like
   * @param  {[type]} applicationApiKey [api key of the application]
   * @return {[type]}                   [description]
   */
  updateConsentHTMLSQSPayload: (applicationApiKey) => {
    logger.info(`updateConsentHTMLSQSPayload()`)
    return new Promise(async (resolve, reject) => {
      try {
        const url = `${microServiceSBApiBasePath}sandbox/v1/consent/generator`
        const payload = {
          'applicationId': [applicationApiKey]
        }

        const consentHTMLPayload = {
          url,
          method: `POST`,
          headers: {
            'Content-Type': 'application/json;charset=utf-8',
            'accept-language': 'en',
            'host': hostHeader
          },
          json: true,
          body: payload
        }
        logger.debug(`consentHTMLPayload: ${JSON.stringify(consentHTMLPayload)}`)
        resolve(consentHTMLPayload)
      } catch (error) {
        logger.error(`updateConsentHTMLSQSPayload() error - ${error}`)
        reject(error)
      }
    }) // End Promise()
  }, // End updateConsentHTMLSQSPayload()
  generateCustomFieldsValues: async (fieldNames, s3BucketKey, arrayObjectCustomFields) => {
    const customFields = await getCustomFieldsByName({ Name: fieldNames })
    if (!customFields || Object.keys(customFields).length !== fieldNames.length) {
      logger.error(`CUSTOM FIELD MISSING: One or more of the custom fields [${fieldNames}] does not exist`)
      throw makeError()
    }

    let applicationIcon = arrayObjectCustomFields.results.find(item => item.Name === APPLICATION_ICON)
    if (!applicationIcon) {
      applicationIcon = {
          Uuid: uuidv4(),
          CustomFieldUuid: customFields[APPLICATION_ICON],
          EntityUuid: uuidv4(),
          Value: s3BucketKey,
      };
      arrayObjectCustomFields.results.push(applicationIcon);
    }
    applicationIcon.Value = s3BucketKey || '';
    return arrayObjectCustomFields
  } // End generateCustomFieldsValues()
}
