const logger = requireSrc('log')
let Validator = require('fastest-validator')

module.exports = {
  validateInput: (req) => {
    logger.debug(`validateInput()`)
    // Validate application payload
    const payload = req.body
    const params = req.params
    // const englishThaiCharacterRegExp = new RegExp('^[^-\s][a-z A-Z\u0E00-\u0E7F]+$')

    // Check application_uuid on param
    if (typeof params.application_uuid === 'undefined' || params.application_uuid === null) {
      logger.debug('APPLICATION UUID IS NOT DEFINED OR IS NULL')
      return { error: { msg_key: 'missing_param', details: 'applicationUuid is undefined' } }
    }

    let v = new Validator()
    let applicationSchema = {
      application: {
        type: 'object',
        props: {
          applicationName: { type: 'string', empty: false, min: 1, max: 50 },
          description: { type: 'string', empty: true, max: 255 },
          callbackUrl: { type: 'string', empty: true, max: 255 }
        }
      }
    }

    let applicationValidatorResult = v.validate(payload, applicationSchema)

    //Validate ApplicatinName will not contains double space
    const regex = /[\s]{2,}|[\t\r\n]+|^\s+|\s+$/ //Must not contain double spaces
    if (regex.test(payload.application.applicationName)) {
      logger.debug(`INVALID PARAM: applicationName contains invalid character`)
      return { error: { msg_key: 'invalid_param', details: 'applicationName should not contains double spaces and not allow to start/end with space' } }
    }

   // Validate callbackPayment and callbackRegister start with "http://" or "https://"
   const merchantPayload = payload.merchants;
   const pathRegex = /^https?:\/\//;
    for (let i = 0; i < merchantPayload.length; i++) {
      if ((merchantPayload[i].callbackPayment && !pathRegex.test(merchantPayload[i].callbackPayment))
        || (merchantPayload[i].callbackRegister && !pathRegex.test(merchantPayload[i].callbackRegister))) {
          return {
            error: {
              msg_key: "generic_server_error",
              details: "Generic server side error",
            }
          };
      }
   }

    
    // Validator function checker
    if (applicationValidatorResult !== true) {
      const errorMessage = applicationValidatorResult[0].message
      if (applicationValidatorResult[0].type === 'required') {
        logger.debug(`MISSING REQUIRED FIELD: ${errorMessage}`)
        return { error: { msg_key: 'missing_param', details: errorMessage } }
      } else {
        logger.debug(`INVALID PARAM: ${errorMessage}`)
        return { error: { msg_key: 'invalid_param', details: errorMessage } }
      }
    }

    return { data: { inputPayload: payload } }
  },
  validateNewMerchantsInput: (req) => {
    logger.debug(`validateNewMerchantsInput()`)
    // NOTE: This is use to validate to existing application where no merchants exist
    // Use this to validate where required to validate new merchants creation
    const payload = req.body
    let v = new Validator()

    let merchantsSchema = {
      merchants: { type: 'array',
        min: 1,
        max: 1,
        items: {
          type: 'object',
          props: {
            merchantName: { type: 'string', empty: true, min: 0, max: 25 },
            billerName: { type: 'string', empty: true, min: 0, max: 25 },
            // portal_applicationId: { type: 'string', empty: false }, // Required validation of UUIDv4 format
            referenceType: { type: 'enum', empty: false, values: ['1', '2'] },
            merchantLegoName: { type: 'string', empty: true, min: 0, max: 25 },
            callbackRegister: { type: 'string', empty: true, optional: true, min: 0, max: 255 },
            callbackPayment: { type: 'string', empty: true, optional: true, min: 0, max: 255 },
            notifications: { type: 'array',
              min: 1,
              max: 1,
              items: {
                type: 'object',
                props: {
                  callbackUrl: { type: 'string', empty: true },
                  default: { type: 'boolean', empty: false }
                }
              }
            }
          }
        }
      }
    }

    let merchantsValidatorResult = v.validate(payload, merchantsSchema)

    // Validator function checker
    if (merchantsValidatorResult !== true) {
      const errorMessage = merchantsValidatorResult[0].message
      if (merchantsValidatorResult[0].type === 'required') {
        logger.debug(`MISSING REQUIRED FIELD: ${errorMessage}`)
        return { error: { msg_key: 'missing_param', details: errorMessage } }
      } else {
        logger.debug(`INVALID PARAM: ${errorMessage}`)
        return { error: { msg_key: 'invalid_param', details: errorMessage } }
      }
    }

    return { data: { inputPayload: payload } }
  }, // End validateNewMerchantsInput()
  validateExistingMerchantsInput: (req) => {
    logger.debug(`validateExistingMerchantsInput()`)
    // NOTE: This is to validate existing application with merchants existed
    // NOTE: For now it only allow 1 merchant and 1 notification object in array
    const payload = req.body
    let v = new Validator()

    let merchantsSchema = {
      merchants: { type: 'array',
        min: 1,
        max: 1,
        items: {
          type: 'object',
          props: {
            merchantId: { type: 'string', empty: false },
            merchantName: { type: 'string', empty: false, min: 0, max: 25 },
            // billerId: { type: 'string', empty: false },
            billerName: { type: 'string', empty: false, min: 0, max: 25 },
            referenceType: { type: 'enum', empty: false, values: ['1', '2'] },
            notifications: { 
              type: 'array',
              min: 1,
              max: 1,
              items: {
                type: 'object',
                props: {
                  callbackUrl: { type: 'string', empty: true },
                  default: { type: 'boolean', empty: false }
                }
              }
            }
          }
        }
      }
    }

    let merchantsValidatorResult = v.validate(payload, merchantsSchema)

    // Validator function checker
    if (merchantsValidatorResult !== true) {
      const errorMessage = merchantsValidatorResult[0].message
      if (merchantsValidatorResult[0].type === 'required') {
        logger.debug(`MISSING REQUIRED FIELD: ${errorMessage}`)
        return { error: { msg_key: 'missing_param', details: errorMessage } }
      } else {
        logger.debug(`INVALID PARAM: ${errorMessage}`)
        return { error: { msg_key: 'invalid_param', details: errorMessage } }
      }
    }

    return { data: { inputPayload: payload } }
  } // End validateExistingMerchantsInput()
}
