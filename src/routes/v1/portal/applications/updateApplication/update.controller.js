const logger = requireSrc('log');
const { getApplication, getCustomFieldsByName, updateApplication } = requireSrc('model').Apim; 
const  { validateInput }  = require('./update.validator')
const { successResponse, failureResponse } = requireSrc('response').Handlers;
const { TWO_WAY_SSL_MAPPING } = require('./update.constants').customFields;
const { generateUpdateApplicationPayload } = require('./update.methods');

 // PUT /partners/v1/portal/applications 
 // for update twoWaySslMapping
module.exports = async (req, res, next) => {
    logger.info('UPDATE APPLICATION')
    try {
        // Validate data
        const data = validateInput(req);

        const { identifyType, identifyValue, twoWaySslMapping }  = data;
        let applicationData; 
        
        // Retrieve application data by call APIM api
        switch (identifyType) {
            case 'APPLICATION_UUID' :
                applicationData = await getApplication({ uuid: identifyValue })
            break;
        }

        // Retrieve CustomFieldsUuid
         const customFieldUuids = await getCustomFieldsByName({ Name: TWO_WAY_SSL_MAPPING });

         // Generate payload application data with the new customField value
         const updateApplicationPayload =  generateUpdateApplicationPayload( applicationData, customFieldUuids[TWO_WAY_SSL_MAPPING], twoWaySslMapping);
         
        await updateApplication(updateApplicationPayload);

        return successResponse(res, next, 'success');  
    }
    catch (error) {
        const errorText = JSON.stringify(error);
        if ( error.response && error.response.status === 404 ) {
            return failureResponse(res, next, 'application_not_found');
        }
        logger.error(`UPDATE APPLICATION ERR: ${errorText !== '{}' ? errorText : error}`);
        return failureResponse(res, next, error.type || 'generic_server_error', error.details || 'Generic server side error');
    }

}