const uuidv4 = require('uuid/v4');

module.exports = {
    /**
     * @param {Object} application  application data  getting from APIM api
     * @param {string} customFieldUuid  customFields UUID that refer to each customFields in cap portal
     * @param {string} data the data that will put into customFields getting from  request payload
     * this method is use for update in each customField
     */
    generateUpdateApplicationPayload: (application, customFieldUuid, data) => {
        // find updated customFields which customFieldUuid
        let customFieldValues = application.CustomFieldValues.results.find(item => item.CustomFieldUuid === customFieldUuid);
       
        // if this application didn't have this customField before the create the new one
        if (!customFieldValues) {
            customFieldValues = {
                Uuid: uuidv4(),
                CustomFieldUuid: customFieldUuid,
                EntityUuid: uuidv4(),
            };
            // add new customField in application
            application.CustomFieldValues.results.push(customFieldValues);
        }
        customFieldValues.Value = data || '';

        return application;
    }
};
