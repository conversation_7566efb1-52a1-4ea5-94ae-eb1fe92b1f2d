const Validator = require('fastest-validator');
const { makeError } = requireSrc('util').CustomError;

module.exports = {
  /**
   * @param {Object} req
   * @param {Object} req.body  request body payload to validate
   * @param {Object} req.identifyType  key for in search APIM api possibleValue is 'APPLICATION_UUID'
   * @param {Object} req.identifyValue  value for search in APIM api possibleValue is 'APPLICATION_UUID'
   * @param {Object} req.twoWaySslMapping the Uuid that's mapping with CSR certificate in ca portal
   */
  validateInput: (req) => {
    const body = req.body;

    if (!body) {
      throw makeError("missing_param", "Request body is missing");
    }

    if ( body && req.body.identifyType) {
        body.identifyType =  req.body.identifyType.toUpperCase();
        console.log(body.identifyType)
    }
    
    const validationSchema = {
        identifyType: { type: 'string', enum: ['APPLICATION_UUID'], empty: false },
        identifyValue: { type: 'string', empty: false },
        twoWaySslMapping: { type: 'string', empty: false },
    };

    const validator = new Validator();
    const validationResult = validator.validate(body, validationSchema);

    if(validationResult !== true) {
        let type = 'missing_param';
        if(validationResult[0].type !== 'required') {
          type = 'invalid_param';
        }
        throw makeError(type, validationResult[0].message);
    }
    return {...body};
  }
}
