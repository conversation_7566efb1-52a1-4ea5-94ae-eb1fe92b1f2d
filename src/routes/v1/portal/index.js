const { Controller } = Commons;
const { apiVersion, apiBasePath } = requireSrc('config')

const Swagger = require('./swagger')
const DeleteApplications = require('./applications/delete')
const CreateApplication = require('./applications/create')
const UpdateApplicationUuid = require('./applications/update')
const UpdateApplication = require('./applications/updateApplication')
const GetOneApplication = require('./applications/getOne')
const CheckOrganization = require('./applications/checkOrg')
const CheckApplication = require('./applications/checkApp')
const GetAppInfo = require('./applications/getAppInfo')

const CreateLiveApplication = require('./applications/live/create')
const UpdateLiveApplication = require('./applications/live/update')
const GetOneLiveApplication = require('./applications/live/getOne')
const UpdateCustomFields = require('./applications/live/customfields/update')
const GetCustomFieldPayload = require('./applications/live/customfields/payload/get')
const UpdateCustomFieldPayload = require('./applications/live/customfields/payload/update')

const basePath = `/${apiVersion}/${apiBasePath}`

// Swagger Endpoint
const ctrlSwagger = new Controller();
ctrlSwagger.method = 'GET'
ctrlSwagger.path = `${basePath}/applications/swagger`
ctrlSwagger.handler = Swagger

// NOTE: Should put swagger before GET :id endpoint

// GET portal/applications/checkOrg
const ctrlCheckOrg = new Controller();
ctrlCheckOrg.method = 'GET'
ctrlCheckOrg.path = `${basePath}/applications/checkOrg`
ctrlCheckOrg.handler = CheckOrganization

// GET portal/applications/checkApp
const ctrlCheckApplication = new Controller();
ctrlCheckApplication.method = 'GET'
ctrlCheckApplication.path = `${basePath}/applications/checkApp`
ctrlCheckApplication.handler = CheckApplication

// POST portal/applications/live
const ctrlCheckLiveApplication = new Controller();
ctrlCheckLiveApplication.method = 'POST'
ctrlCheckLiveApplication.path = `${basePath}/applications/live`
ctrlCheckLiveApplication.handler = CreateLiveApplication

// PUT portal/applications/live
const ctrlUpdateLiveApplication = new Controller();
ctrlUpdateLiveApplication.method = 'PUT'
ctrlUpdateLiveApplication.path = `${basePath}/applications/live`
ctrlUpdateLiveApplication.handler = UpdateLiveApplication

// GET portal/applications/live/:applicationUuid
const ctrlGetOneLiveApplication = new Controller();
ctrlGetOneLiveApplication.method = 'GET'
ctrlGetOneLiveApplication.path = `${basePath}/applications/live/:portalApplicationUuid`
ctrlGetOneLiveApplication.handler = GetOneLiveApplication

// PUT portal/applications/live/customfields
const ctrlUpdateCustomFields = new Controller();
ctrlUpdateCustomFields.method = 'PUT'
ctrlUpdateCustomFields.path = `${basePath}/applications/live/customfields`
ctrlUpdateCustomFields.handler = UpdateCustomFields

// GET portal/applications/live/customfields/payload
const ctrlGetCustomFieldPayload = new Controller();
ctrlGetCustomFieldPayload.method = 'GET'
ctrlGetCustomFieldPayload.path = `${basePath}/applications/live/customfields/payload`
ctrlGetCustomFieldPayload.handler = GetCustomFieldPayload

// PUT portal/applications/live/customfields/payload
const ctrlUpdateCustomFieldPayload = new Controller();
ctrlUpdateCustomFieldPayload.method = 'PUT'
ctrlUpdateCustomFieldPayload.path = `${basePath}/applications/live/customfields/payload`
ctrlUpdateCustomFieldPayload.handler = UpdateCustomFieldPayload

// DELETE portal/applications
const ctrlDeleteApplications = new Controller();
ctrlDeleteApplications.method = 'DELETE'
ctrlDeleteApplications.path = `${basePath}/applications/:application_uuid`
ctrlDeleteApplications.handler = DeleteApplications

// PUT portal/applications/applicationUuid
const ctrlUpdateApplicationUuid = new Controller();
ctrlUpdateApplicationUuid.method = 'PUT'
ctrlUpdateApplicationUuid.path = `${basePath}/applications/:application_uuid`
ctrlUpdateApplicationUuid.handler = UpdateApplicationUuid

// GET portal/applications/applicationUuid
const ctrlGetOneApplication = new Controller();
ctrlGetOneApplication.method = 'GET'
ctrlGetOneApplication.path = `${basePath}/applications/:application_uuid`
ctrlGetOneApplication.handler = GetOneApplication

// PUT portal/applications
const ctrlUpdateApplication = new Controller();
ctrlUpdateApplication.method = 'PUT'
ctrlUpdateApplication.path = `${basePath}/applications`
ctrlUpdateApplication.handler = UpdateApplication

// GET portal/applications?{param}={value}
const ctrlGetAppInfo = new Controller();
ctrlGetAppInfo.method = 'GET'
ctrlGetAppInfo.path = `${basePath}/applications`
ctrlGetAppInfo.handler = GetAppInfo

// POST portal/application
const ctrlCreateApplication = new Controller();
ctrlCreateApplication.method = 'POST'
ctrlCreateApplication.path = `${basePath}/applications`
ctrlCreateApplication.handler = CreateApplication

module.exports = [
    ctrlSwagger,
    ctrlCheckOrg,
    ctrlCheckApplication,
    ctrlCheckLiveApplication,
    ctrlUpdateLiveApplication,
    ctrlGetOneLiveApplication,
    ctrlUpdateCustomFields,
    ctrlGetCustomFieldPayload,
    ctrlUpdateCustomFieldPayload,
    ctrlDeleteApplications,
    ctrlUpdateApplicationUuid,
    ctrlGetOneApplication,
    ctrlUpdateApplication,
    ctrlGetAppInfo,
    ctrlCreateApplication
]
