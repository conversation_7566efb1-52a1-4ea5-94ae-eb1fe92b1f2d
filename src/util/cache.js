const NodeCache = require('node-cache');

const { cacheTTL } = requireSrc('config')
const logger = requireSrc('log');

// https://medium.com/@danielsternlicht/caching-like-a-boss-in-nodejs-9bccbbc71b9b
class Cache {

  constructor() {
    this.cache = new NodeCache({ stdTTL: cacheTTL, checkperiod: cacheTTL * 0.2, useClones: false });
    logger.debug(`INIT cache: { stdTTL: ${cacheTTL}, checkperiod: ${cacheTTL * 0.2} }`);
  }

  get(key, storeFunction) {
    const value = this.cache.get(key);
    if (value) {
        logger.debug(`Cache HIT: ${key}, value: ${JSON.stringify(value)}`);
        return Promise.resolve(value);
    }

    return storeFunction()
        .then((result) => {
            logger.debug(`Cache MISS: ${key}, storing value: ${JSON.stringify(result)}`);
            this.cache.set(key, result);
            return result;
        })
        .catch( error => {
            logger.error(`Cache store lookup ERROR: ${JSON.stringify(error)}`);
            return error; 
        } );
  }

  del(keys) {
    this.cache.del(keys);
    logger.debug(`Cache DELETE: ${keys}`);
  }

  delStartWith(startStr = '') {
    if (!startStr) {
      return;
    }

    const keys = this.cache.keys();
    for (const key of keys) {
      if (key.indexOf(startStr) === 0) {
        this.del(key);
      }
    }
  }

  stats() {
    return this.cache.getStats();
  }

  flush() {
    logger.debug(`Cache BEFORE FLUSH: ${JSON.stringify(this.cache.getStats())}`);
    this.cache.flushAll();
    logger.debug(`Cache FLUSHED: ${JSON.stringify(this.cache.getStats())}`);
  }
}


module.exports = Cache;