const logger = requireSrc('log');

module.exports = {
    validateResponse: (response) => {
        const http = response.statusCode;
        if (http && +http >= 400) {
            logger.debug('DOWNSTREAM HTTP ERROR');
            return { error: { msg_key: 'invalid_downstream_response', details: {reason: 'Downstream http error', source: response.request.uri.path, http_code: http, response: response.body } } }
        }

        const body = response.body;
        if (!body || !body.status) {
            logger.debug('UNEXPECTED DOWNSTREAM RESPONSE');
            return { error: { msg_key: 'invalid_downstream_response', details: {reason: 'Invalid downstream response structure', source: response.request.uri.path, http_code: http, response: response.body } } }
        }

        const code = body.status.code;
        if (!code) {
            logger.debug('CODE NOT FOUND');
            return { error: { msg_key: 'invalid_downstream_response', details: {reason: 'Business response code not found', source: response.request.uri.path, response: response.body } }, data: body.data }
        }
        else if (+code === 1000) {
            return { data: body.data };
        }
        else { // code !== 1000
            logger.debug('DOWNSTREAM CODE NOT 1000')
            return { error: { msg_key: 'invalid_downstream_response', details: {reason: 'Business response code error', source: response.request.uri.path, downstream_error: body.status} }, data: body.data }
        }
    }
}