const logger = requireSrc('log');
const AWS = require('aws-sdk');
const { aws } = requireSrc('config');

const getS3Instance = () => {
  const { region, s3: { apiVersion, signatureVersion, } } = aws;
  return new AWS.S3({ apiVersion, signatureVersion, region });
}

const getS3File = () => {
  return new Promise(async (resolve, reject) => {
    try {
      logger.info(`getS3File()`);
      const s3 = getS3Instance();
      const { bucketName, folderName, fileName } = aws.s3;
      const key = `${folderName}/${fileName}`;
      const params = { Bucket: bucketName, Key: key };

      logger.debug(`Downloading file: ${bucketName}/${key}`);
      
      s3.getObject(params, (error, data) => {
        if (error) {
          logger.error(`S3GETOBJECT ERROR: ${error}`);
          return reject({ type: 'generic_server_error', details: error.message });
        }
        logger.debug(`Download complete`);
        
        return resolve(JSON.parse(data.Body));
      });
    } catch (error) {
      logger.error(`GETS3FILE ERROR: ${error}`);
      return reject({ type: 'generic_server_error', details: error.message });
    }
  });      
};

const updateS3File = (data) => {
  return new Promise(async (resolve, reject) => {
    try {
      logger.info(`uploadS3File()`);
      const s3 = getS3Instance();
      const contentString = JSON.stringify(data);
      const { bucketName, folderName, fileName } = aws.s3;
      const key = `${folderName}/${fileName}`;
      const params = {
        Bucket: bucketName,
        Key: key,
        ContentType: 'application/json',
        Body: contentString,
      };

      logger.debug(`Uploading file: ${bucketName}/${key}`);
      
      s3.putObject(params, (error, data) => {
        if (error) {
          logger.error(`S3PUTOBJECT ERROR: ${error}`);
          return reject({ type: 'generic_server_error', details: error.message });
        }
        logger.debug(`Upload complete`);
        return resolve();
      });
    } catch (error) {
      logger.error(`UPLOADS3FILE ERROR: ${error}`);
      return reject({ type: 'generic_server_error', details: error.message });
    }
  });      
};

module.exports = {
  getS3File,
  updateS3File,
};