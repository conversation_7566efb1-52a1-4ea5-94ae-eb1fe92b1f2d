const AWS = require('aws-sdk')
const { aws } = requireSrc('config')
const logger = requireSrc('log')
AWS.config.update({
  region: aws.region
})

var sqs = new AWS.SQS({
  apiVersion: aws.apiVersion
})

module.exports = {
  /**
   * Function writing to AWS SQS
   * @param  {[type]}  payload [JSON Payload]
   * @param  {[type]}  title   [Sample function title]
   * @return {Promise}         [description]
   */
  writeSQS: async (payload, title) => {
    logger.info(`writeSQS()`)
    return new Promise((resolve, reject) => {
      try {
        const payloadStringify = JSON.stringify(payload)
        let sqsParams = {
          MessageAttributes: {
            'Title': {
              DataType: 'String',
              StringValue: title
            }
          },
          QueueUrl: aws.queueURL,
          MessageBody: payloadStringify
        }
        logger.debug(`sqsParams: ${JSON.stringify(sqsParams)}`)

        sqs.sendMessage(sqsParams, (err, data) => {
          logger.debug('WRITING TO SQS QUEUE')
          if (err) {
            logger.error(`WRITE TO SQS ERROR: ${err}`)
            resolve()
          } else {
            logger.debug(`SUCCESSFUL WRITE TO SQS: ${data.MessageId}`)
            resolve()
          }
        }) // End sqs.sendMessage()
      } catch (error) {
        logger.error(`WRITE TO SQS ERROR: ${JSON.stringify(error)}`)
        resolve()
      }
    }) // End Promise()
  } // End write()
} // End exports()
