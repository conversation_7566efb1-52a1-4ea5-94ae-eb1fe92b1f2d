const createApplicationHandler = require("../src/routes/v1/portal/applications/create/create.controller.js");
jest.mock("../src/response/handler.js", () => ({
  successResponse: jest.fn(),
  failureResponse: jest.fn(),
  missingParamsResponse: jest.fn(),
  forwardFailureResponse: jest.fn(),
}));

jest.mock("../src/model/applications/applicationInfo-db.js", () => ({
  lookupNonDeletedApplicationNameIfExist: jest.fn(),
  countNonDeletedApplicationByOrganizationUuid: jest.fn(),
  createOneApplicationRecord: jest.fn(),
  removeOneApplicationRecordByUuid: jest.fn(),
}));

jest.mock("../src/model/applications/applicationUsers-db.js", () => ({
  addOneApplicationUsers: jest.fn(),
  removeApplicationUserRecordByAppId: jest.fn(),
}));

jest.mock("../src/model/apim", () => ({
  getApplicationsForOrganization: jest.fn(),
  getCustomFieldsByName: jest.fn(),
  createApplication: jest.fn(),
  deleteApplication: jest.fn(),
}));

jest.mock("../src/model/merchants/merchant-api.js", () => ({
  createMerchantAPI: jest.fn(),
}));

jest.mock("../src/model/transfer/transferCoperate-db.js", () => ({
  getTransferCoperate: jest.fn(),
}));

jest.mock("../src/model/transfer/transferAccount-db.js", () => ({
  insertMockAccounts: jest.fn(),
}));

jest.mock("../src/util/sqs.js", () => ({
  writeSQS: jest.fn(),
}));

jest.mock("../src/routes/v1/portal/applications/create/create.methods", () => ({
  verifyOrgAppLimit: jest.fn(),
  verifyAppLimit: jest.fn(),
  validateApiGroupsUuid: jest.fn(),
  generateCreateAppDBJSON: jest.fn(),
  generateCreateAppCAJSON: jest.fn(),
  createMockProfile: jest.fn(),
  createMockMerchantProfile: jest.fn(),
  generateMerchantsJSON: jest.fn(),
  createConsentHTMLSQSPayload: jest.fn(),
}));

jest.mock(
  "../src/routes/v1/portal/applications/create/create.validator",
  () => ({
    validateInput: jest.fn(),
  })
);

jest.mock(
  "src/log",
  () => ({
    info: jest.fn(),
    debug: jest.fn(),
    error: jest.fn(),
  }),
  { virtual: true }
);

const {
  successResponse,
  failureResponse,
  missingParamsResponse,
  forwardFailureResponse,
} = require("../src/response/handler.js");
const {
  lookupNonDeletedApplicationNameIfExist,
  countNonDeletedApplicationByOrganizationUuid,
  createOneApplicationRecord,
  removeOneApplicationRecordByUuid,
} = require("../src/model/applications/applicationInfo-db.js");
const {
  addOneApplicationUsers,
  removeApplicationUserRecordByAppId,
} = require("../src/model/applications/applicationUsers-db.js");
const {
  getApplicationsForOrganization,
  getCustomFieldsByName,
  createApplication,
  deleteApplication,
} = require("../src/model/apim");
const { createMerchantAPI } = require("../src/model/merchants/merchant-api.js");
const {
  getTransferCoperate,
} = require("../src/model/transfer/transferCoperate-db.js");
const {
  insertMockAccounts,
} = require("../src/model/transfer/transferAccount-db.js");
const { writeSQS } = require("../src/util/sqs.js");
const {
  verifyOrgAppLimit,
  verifyAppLimit,
  validateApiGroupsUuid,
  generateCreateAppDBJSON,
  generateCreateAppCAJSON,
  createMockProfile,
  createMockMerchantProfile,
  generateMerchantsJSON,
  createConsentHTMLSQSPayload,
} = require("../src/routes/v1/portal/applications/create/create.methods");
const {
  validateInput,
} = require("../src/routes/v1/portal/applications/create/create.validator");

console.log = jest.fn();

describe("Create Application Handler", () => {
  let req, res, next;

  beforeEach(() => {
    jest.clearAllMocks();
    req = {
      headers: {
        authorization: "Bearer test-token",
        organizationuuid: "org-uuid",
        useruuid: "user-uuid",
      },
      body: {
        application: {
          applicationName: "Test App",
          apiGroupIds: ["uuid1", "uuid2"],
        },
        merchants: [{ merchantName: "Test Merchant" }],
      },
    };
    res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };
    next = jest.fn();
  });

  describe("Successful creation flow", () => {
    beforeEach(() => {
      validateInput.mockReturnValue({
        data: {
          inputPayload: {
            application: {
              applicationName: "Test App",
              apiGroupIds: ["uuid1", "uuid2"],
            },
            merchants: [{ merchantName: "Test Merchant" }],
          },
        },
        error: null,
      });
      lookupNonDeletedApplicationNameIfExist.mockResolvedValue(true);
      getApplicationsForOrganization.mockResolvedValue(null);
      countNonDeletedApplicationByOrganizationUuid.mockResolvedValue({
        numOfExistingApp: 1,
      });
      verifyAppLimit.mockResolvedValue(true);
      verifyOrgAppLimit.mockResolvedValue(true);
      validateApiGroupsUuid.mockResolvedValue(true);
      getCustomFieldsByName.mockResolvedValue({
        APPLICATION_ICON: "custom-field-uuid",
      });
      generateCreateAppCAJSON.mockResolvedValue({
        Uuid: "app-uuid",
        ApiKey: "test-api-key",
      });
      createApplication.mockResolvedValue({
        Status: "ENABLED",
      });
      generateCreateAppDBJSON.mockResolvedValue({
        uuid: "app-uuid",
        applicationName: "Test App",
      });
      createOneApplicationRecord.mockResolvedValue(true);
      addOneApplicationUsers.mockResolvedValue(true);
      generateMerchantsJSON.mockResolvedValue([
        { merchantName: "Test Merchant" },
      ]);
      createMerchantAPI.mockResolvedValue({
        statusCode: 200,
        body: { success: true },
      });
      createMockProfile.mockResolvedValue(true);
      createMockMerchantProfile.mockResolvedValue(true);
      getTransferCoperate.mockResolvedValue([
        { corporateId: "corp-1" },
        { corporateId: "corp-2" },
      ]);
      insertMockAccounts.mockResolvedValue(true);
      createConsentHTMLSQSPayload.mockResolvedValue({ mockPayload: true });
      writeSQS.mockResolvedValue(true);
      successResponse.mockImplementation((res, next, key, data) => {
        res.status(200).json({ success: true, key, data });
      });
    });

    test("should successfully create application with all resources", async () => {
      await createApplicationHandler(req, res, next);

      expect(validateInput).toHaveBeenCalledWith(req);
      expect(lookupNonDeletedApplicationNameIfExist).toHaveBeenCalledWith(
        "Test App",
        "org-uuid"
      );
      expect(getApplicationsForOrganization).toHaveBeenCalledWith({
        organizationUuid: "org-uuid",
      });
      expect(countNonDeletedApplicationByOrganizationUuid).toHaveBeenCalledWith(
        "org-uuid"
      );
      expect(validateApiGroupsUuid).toHaveBeenCalledWith(["uuid1", "uuid2"]);
      expect(getCustomFieldsByName).toHaveBeenCalledWith({
        Name: ["ApplicationIcon"],
      });
      expect(generateCreateAppCAJSON).toHaveBeenCalledWith(
        req.body.application,
        { APPLICATION_ICON: "custom-field-uuid" },
        "org-uuid"
      );
      expect(createApplication).toHaveBeenCalled();
      expect(createOneApplicationRecord).toHaveBeenCalled();
      expect(addOneApplicationUsers).toHaveBeenCalledWith(
        "app-uuid",
        "user-uuid"
      );
      expect(generateMerchantsJSON).toHaveBeenCalledWith(
        req.body.merchants,
        "app-uuid"
      );
      expect(createMerchantAPI).toHaveBeenCalledWith({
        merchants: [{ merchantName: "Test Merchant" }],
      });
      expect(createMockProfile).toHaveBeenCalledWith("app-uuid", "user-uuid");
      expect(createMockMerchantProfile).toHaveBeenCalledWith(
        "app-uuid",
        "user-uuid"
      );
      expect(getTransferCoperate).toHaveBeenCalled();
      expect(insertMockAccounts).toHaveBeenCalledWith("corp-1", "app-uuid");
      expect(createConsentHTMLSQSPayload).toHaveBeenCalledWith("test-api-key");
      expect(writeSQS).toHaveBeenCalledWith(
        { mockPayload: true },
        "Create Consent HTML Sandbox"
      );
      expect(successResponse).toHaveBeenCalledWith(
        res,
        next,
        "application_created",
        {
          applicationUuid: "app-uuid",
          status: "ENABLED",
        }
      );
    });
  });

  describe("Error handling", () => {
    test("should handle validation errors", async () => {
      validateInput.mockReturnValue({
        data: null,
        error: {
          details: "Missing required fields",
          msg_key: "validation_error",
        },
      });

      await createApplicationHandler(req, res, next);

      expect(missingParamsResponse).toHaveBeenCalledWith(
        res,
        next,
        "validation_error",
        { message: "Missing required fields" }
      );
    });

    test("should handle merchant creation failure and cleanup", async () => {
      validateInput.mockReturnValue({
        data: {
          inputPayload: {
            application: req.body.application,
            merchants: req.body.merchants,
          },
        },
        error: null,
      });

      lookupNonDeletedApplicationNameIfExist.mockResolvedValue(true);
      getApplicationsForOrganization.mockResolvedValue(null);
      countNonDeletedApplicationByOrganizationUuid.mockResolvedValue({
        numOfExistingApp: 1,
      });
      verifyAppLimit.mockResolvedValue(true);
      verifyOrgAppLimit.mockResolvedValue(true);
      validateApiGroupsUuid.mockResolvedValue(true);
      getCustomFieldsByName.mockResolvedValue({
        APPLICATION_ICON: "custom-field-uuid",
      });

      generateCreateAppCAJSON.mockResolvedValue({
        Uuid: "app-uuid",
        ApiKey: "test-api-key",
      });

      createApplication.mockResolvedValue({ Status: "ENABLED" });
      generateCreateAppDBJSON.mockResolvedValue({ uuid: "app-uuid" });
      createOneApplicationRecord.mockResolvedValue(true);
      addOneApplicationUsers.mockResolvedValue(true);
      generateMerchantsJSON.mockResolvedValue([
        { merchantName: "Test Merchant" },
      ]);
      createMerchantAPI.mockResolvedValue({
        statusCode: 400,
        body: { error: "Merchant creation failed" },
      });

      deleteApplication.mockResolvedValue(true);
      removeOneApplicationRecordByUuid.mockResolvedValue(true);
      removeApplicationUserRecordByAppId.mockResolvedValue(true);

      await createApplicationHandler(req, res, next);

      expect(deleteApplication).toHaveBeenCalledWith({ uuid: "app-uuid" });
      expect(removeOneApplicationRecordByUuid).toHaveBeenCalledWith("app-uuid");
      expect(removeApplicationUserRecordByAppId).toHaveBeenCalledWith(
        "app-uuid"
      );
      expect(forwardFailureResponse).toHaveBeenCalledWith(res, next, 400, {
        error: "Merchant creation failed",
      });
    });

    test("should handle generic server errors", async () => {
      validateInput.mockReturnValue({
        data: {
          inputPayload: {
            application: req.body.application,
            merchants: req.body.merchants,
          },
        },
        error: null,
      });
      lookupNonDeletedApplicationNameIfExist.mockRejectedValue(
        new Error("Database connection failed")
      );

      await createApplicationHandler(req, res, next);

      expect(failureResponse).toHaveBeenCalledWith(
        res,
        next,
        "generic_server_error",
        { error: expect.any(Error) }
      );
    });
  });

  test("should handle missing custom fields error", async () => {
    validateInput.mockReturnValue({
      data: {
        inputPayload: {
          application: req.body.application,
          merchants: req.body.merchants,
        },
      },
      error: null,
    });

    lookupNonDeletedApplicationNameIfExist.mockResolvedValue(true);
    getApplicationsForOrganization.mockResolvedValue(null);
    countNonDeletedApplicationByOrganizationUuid.mockResolvedValue({
      numOfExistingApp: 1,
    });
    verifyAppLimit.mockResolvedValue(true);
    verifyOrgAppLimit.mockResolvedValue(true);
    validateApiGroupsUuid.mockResolvedValue(true);
    getCustomFieldsByName.mockResolvedValue(null);

    await createApplicationHandler(req, res, next);

    expect(getCustomFieldsByName).toHaveBeenCalledWith({
      Name: ["ApplicationIcon"],
    });
    expect(failureResponse).toHaveBeenCalledWith(
      res,
      next,
      "generic_server_error",
      { error: { type: "generic_server_error" } }
    );
  });
});
