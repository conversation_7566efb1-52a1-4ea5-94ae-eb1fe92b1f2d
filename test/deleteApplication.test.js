const deleteApplicationHandler = require('../src/routes/v1/portal/applications/delete/delete.controller.js')

jest.mock('../src/response/handler.js', () => ({
  successResponse: jest.fn(),
  failureResponse: jest.fn(),
  missingParamsResponse: jest.fn()
}))

jest.mock('../src/model/merchants/merchant-api.js', () => ({
  deleteOneMerchantAPI: jest.fn(),
  getOneMerchantAPI: jest.fn()
}))

jest.mock('../src/model/applications/applicationUsers-db.js', () => ({
  updateOneDeletedApplicationUserByAppId: jest.fn()
}))

jest.mock('../src/model/applications/applicationInfo-db.js', () => ({
  updateOneDeletedApplicationInfoByUuid: jest.fn()
}))

jest.mock('../src/model/apim', () => ({
  getApplication: jest.fn(),
  deleteApplication: jest.fn()
}))

jest.mock('../src/util/sqs.js', () => ({
  writeSQS: jest.fn()
}))

jest.mock('../src/model/transfer/transferAccount-db.js', () => ({
  getAccountByApplicationUuid: jest.fn(),
  deleteAccountByAccountUuid: jest.fn()
}))

jest.mock('../src/model/transfer/transferAccountActivity-db.js', () => ({
  deleteActivityAccountByAccount: jest.fn()
}))

jest.mock('../src/model/transfer/transferBlacklistAccount-db.js', () => ({
  deleteBlacklistAccountByAccount: jest.fn()
}))

jest.mock('../src/routes/v1/portal/applications/delete/delete.methods', () => ({
  deleteConsentHTMLSQSPayload: jest.fn()
}))

jest.mock('../src/model/mockcustomerprofile/customerprofile-api.js', () => ({
  getCustomerProfilesByAppIdAPI: jest.fn(),
  deleteOneMockCustomerProfileByBankRmidAPI: jest.fn()
}))

jest.mock('../src/model/mockmerchantprofile/merchantprofile-api.js', () => ({
  deleteAllMockMerchantProfileByApplicationUuidAPI: jest.fn()
}))

jest.mock('src/log', () => ({
  debug: jest.fn(),
  error: jest.fn()
}), { virtual: true })

const { successResponse, failureResponse, missingParamsResponse } = require('../src/response/handler.js')
const { deleteOneMerchantAPI, getOneMerchantAPI } = require('../src/model/merchants/merchant-api.js')
const { updateOneDeletedApplicationUserByAppId } = require('../src/model/applications/applicationUsers-db.js')
const { updateOneDeletedApplicationInfoByUuid } = require('../src/model/applications/applicationInfo-db.js')
const { getApplication, deleteApplication } = require('../src/model/apim')
const { writeSQS } = require('../src/util/sqs.js')
const { getAccountByApplicationUuid, deleteAccountByAccountUuid } = require('../src/model/transfer/transferAccount-db.js')
const { deleteActivityAccountByAccount } = require('../src/model/transfer/transferAccountActivity-db.js')
const { deleteBlacklistAccountByAccount } = require('../src/model/transfer/transferBlacklistAccount-db.js')
const { deleteConsentHTMLSQSPayload } = require('../src/routes/v1/portal/applications/delete/delete.methods')
const { getCustomerProfilesByAppIdAPI, deleteOneMockCustomerProfileByBankRmidAPI } = require('../src/model/mockcustomerprofile/customerprofile-api.js')
const { deleteAllMockMerchantProfileByApplicationUuidAPI } = require('../src/model/mockmerchantprofile/merchantprofile-api.js')

console.log = jest.fn()

describe('Delete Application Handler', () => {
  let req, res, next

  beforeEach(() => {
    req = {
      params: {
        application_uuid: 'test-app-uuid'
      },
      headers: {
        authorization: 'Bearer mock-token',
        useruuid: 'mock-user-uuid'
      }
    }
    res = {}
    next = jest.fn()
    jest.clearAllMocks()
  })

  describe('Successful deletion flow', () => {
    beforeEach(() => {
      getApplication.mockResolvedValue({ ApiKey: 'test-api-key' })
      deleteApplication.mockResolvedValue()
      deleteAllMockMerchantProfileByApplicationUuidAPI.mockResolvedValue()
      updateOneDeletedApplicationInfoByUuid.mockResolvedValue()
      updateOneDeletedApplicationUserByAppId.mockResolvedValue()
      getOneMerchantAPI.mockResolvedValue([])
      getCustomerProfilesByAppIdAPI.mockResolvedValue({ profiles: [] })
      deleteConsentHTMLSQSPayload.mockResolvedValue({ mockPayload: true })
      writeSQS.mockResolvedValue()
      getAccountByApplicationUuid.mockResolvedValue([])
      deleteActivityAccountByAccount.mockResolvedValue()
      deleteBlacklistAccountByAccount.mockResolvedValue()
      deleteAccountByAccountUuid.mockResolvedValue()
    })

    test('should successfully delete application with no merchants or customer profiles', async () => {
      await deleteApplicationHandler(req, res, next)
      expect(getApplication).toHaveBeenCalledWith({ uuid: 'test-app-uuid' })
      expect(deleteApplication).toHaveBeenCalledWith({ uuid: 'test-app-uuid' })
      expect(deleteAllMockMerchantProfileByApplicationUuidAPI).toHaveBeenCalledWith('test-app-uuid', 'mock-user-uuid')
      expect(updateOneDeletedApplicationInfoByUuid).toHaveBeenCalledWith('test-app-uuid')
      expect(updateOneDeletedApplicationUserByAppId).toHaveBeenCalledWith('test-app-uuid')
      expect(getOneMerchantAPI).toHaveBeenCalledWith('test-app-uuid')
      expect(getCustomerProfilesByAppIdAPI).toHaveBeenCalledWith('test-app-uuid')
      expect(deleteConsentHTMLSQSPayload).toHaveBeenCalledWith('test-api-key')
      expect(writeSQS).toHaveBeenCalledWith({ mockPayload: true }, 'Delete Consent HTML Sandbox')
      expect(getAccountByApplicationUuid).toHaveBeenCalledWith('test-app-uuid')
      expect(deleteActivityAccountByAccount).toHaveBeenCalledWith([])
      expect(deleteBlacklistAccountByAccount).toHaveBeenCalledWith([])
      expect(deleteAccountByAccountUuid).toHaveBeenCalledWith('test-app-uuid')
      expect(successResponse).toHaveBeenCalledWith(res, next, 'success', { message: 'Successfully deleted' })
    })

    test('should successfully delete application with merchants', async () => {
      const mockMerchants = [
        { merchantId: 'merchant-1' },
        { merchantId: 'merchant-2' }
      ]
      getOneMerchantAPI.mockResolvedValue(mockMerchants)
      deleteOneMerchantAPI.mockResolvedValue()

      await deleteApplicationHandler(req, res, next)

      expect(deleteOneMerchantAPI).toHaveBeenCalledTimes(2)
      expect(deleteOneMerchantAPI).toHaveBeenCalledWith('merchant-1')
      expect(deleteOneMerchantAPI).toHaveBeenCalledWith('merchant-2')
      expect(successResponse).toHaveBeenCalledWith(res, next, 'success', { message: 'Successfully deleted' })
    })

    test('should successfully delete application with customer profiles', async () => {
      const mockCustomerProfiles = {
        profiles: [
          { bankRmid: 'rmid-1' },
          { bankRmid: 'rmid-2' }
        ]
      }
      getCustomerProfilesByAppIdAPI.mockResolvedValue(mockCustomerProfiles)
      deleteOneMockCustomerProfileByBankRmidAPI.mockResolvedValue()

      await deleteApplicationHandler(req, res, next)

      expect(deleteOneMockCustomerProfileByBankRmidAPI).toHaveBeenCalledTimes(2)
      expect(deleteOneMockCustomerProfileByBankRmidAPI).toHaveBeenCalledWith('rmid-1', 'mock-user-uuid')
      expect(deleteOneMockCustomerProfileByBankRmidAPI).toHaveBeenCalledWith('rmid-2', 'mock-user-uuid')
      expect(successResponse).toHaveBeenCalledWith(res, next, 'success', { message: 'Successfully deleted' })
    })

    test('should successfully delete application with transfer accounts', async () => {
      const mockAccounts = [
        { account: 'account-1' },
        { account: 'account-2' }
      ]
      getAccountByApplicationUuid.mockResolvedValue(mockAccounts)

      await deleteApplicationHandler(req, res, next)

      expect(deleteActivityAccountByAccount).toHaveBeenCalledWith(['account-1', 'account-2'])
      expect(deleteBlacklistAccountByAccount).toHaveBeenCalledWith(['account-1', 'account-2'])
      expect(deleteAccountByAccountUuid).toHaveBeenCalledWith('test-app-uuid')
      expect(successResponse).toHaveBeenCalledWith(res, next, 'success', { message: 'Successfully deleted' })
    })

    test('should successfully delete application with all resources present', async () => {
      const mockMerchants = [{ merchantId: 'merchant-1' }]
      const mockCustomerProfiles = { profiles: [{ bankRmid: 'rmid-1' }] }
      const mockAccounts = [{ account: 'account-1' }]

      getOneMerchantAPI.mockResolvedValue(mockMerchants)
      getCustomerProfilesByAppIdAPI.mockResolvedValue(mockCustomerProfiles)
      getAccountByApplicationUuid.mockResolvedValue(mockAccounts)
      deleteOneMerchantAPI.mockResolvedValue()
      deleteOneMockCustomerProfileByBankRmidAPI.mockResolvedValue()

      await deleteApplicationHandler(req, res, next)

      expect(deleteOneMerchantAPI).toHaveBeenCalledWith('merchant-1')
      expect(deleteOneMockCustomerProfileByBankRmidAPI).toHaveBeenCalledWith('rmid-1', 'mock-user-uuid')
      expect(deleteActivityAccountByAccount).toHaveBeenCalledWith(['account-1'])
      expect(deleteBlacklistAccountByAccount).toHaveBeenCalledWith(['account-1'])
      expect(successResponse).toHaveBeenCalledWith(res, next, 'success', { message: 'Successfully deleted' })
    })
  })

  describe('Error handling', () => {
    beforeEach(() => {
      req = {
        params: {
          application_uuid: 'test-app-uuid'
        },
        headers: {
          authorization: 'Bearer mock-token',
          useruuid: 'mock-user-uuid'
        }
      }
      res = {}
      next = jest.fn()
      jest.clearAllMocks()
    })
    test('should handle getApplication error', async () => {
      const mockError = new Error('API Error')
      getApplication.mockRejectedValue(mockError)

      await deleteApplicationHandler(req, res, next)

      expect(failureResponse).toHaveBeenCalledWith(
        res, 
        next, 
        'generic_server_error', 
        { error: mockError }
      )
    })

    test('should handle deleteApplication error', async () => {
      getApplication.mockResolvedValue({ ApiKey: 'test-api-key' })
      const mockError = new Error('Delete API Error')
      deleteApplication.mockRejectedValue(mockError)

      await deleteApplicationHandler(req, res, next)

      expect(failureResponse).toHaveBeenCalledWith(
        res, 
        next, 
        'generic_server_error', 
        { error: mockError }
      )
    })

    test('should handle error with type property', async () => {
      const mockError = { 
        type: 'custom_error_type',
        message: 'Custom error message'
      }
      getApplication.mockRejectedValue(mockError)

      await deleteApplicationHandler(req, res, next)

      expect(failureResponse).toHaveBeenCalledWith(
        res, 
        next, 
        'custom_error_type', 
        { error: mockError }
      )
    })

    test('should handle merchant deletion error', async () => {
      getApplication.mockResolvedValue({ ApiKey: 'test-api-key' })
      deleteApplication.mockResolvedValue()
      deleteAllMockMerchantProfileByApplicationUuidAPI.mockResolvedValue()
      updateOneDeletedApplicationInfoByUuid.mockResolvedValue()
      updateOneDeletedApplicationUserByAppId.mockResolvedValue()
      getOneMerchantAPI.mockResolvedValue([{ merchantId: 'merchant-1' }])
      
      const mockError = new Error('Merchant deletion failed')
      deleteOneMerchantAPI.mockRejectedValue(mockError)

      await deleteApplicationHandler(req, res, next)

      expect(failureResponse).toHaveBeenCalledWith(
        res, 
        next, 
        'generic_server_error', 
        { error: mockError }
      )
    })

    test('should handle customer profile deletion error', async () => {
      getApplication.mockResolvedValue({ ApiKey: 'test-api-key' })
      deleteApplication.mockResolvedValue()
      deleteAllMockMerchantProfileByApplicationUuidAPI.mockResolvedValue()
      updateOneDeletedApplicationInfoByUuid.mockResolvedValue()
      updateOneDeletedApplicationUserByAppId.mockResolvedValue()
      getOneMerchantAPI.mockResolvedValue([])
      getCustomerProfilesByAppIdAPI.mockResolvedValue({ profiles: [{ bankRmid: 'rmid-1' }] })
      
      const mockError = new Error('Customer profile deletion failed')
      deleteOneMockCustomerProfileByBankRmidAPI.mockRejectedValue(mockError)

      await deleteApplicationHandler(req, res, next)

      expect(failureResponse).toHaveBeenCalledWith(
        res, 
        next, 
        'generic_server_error', 
        { error: mockError }
      )
    })

    test('should handle SQS payload generation error', async () => {
      getApplication.mockResolvedValue({ ApiKey: 'test-api-key' })
      deleteApplication.mockResolvedValue()
      deleteAllMockMerchantProfileByApplicationUuidAPI.mockResolvedValue()
      updateOneDeletedApplicationInfoByUuid.mockResolvedValue()
      updateOneDeletedApplicationUserByAppId.mockResolvedValue()
      getOneMerchantAPI.mockResolvedValue([])
      getCustomerProfilesByAppIdAPI.mockResolvedValue({ profiles: [] })
      
      const mockError = new Error('SQS payload generation failed')
      deleteConsentHTMLSQSPayload.mockRejectedValue(mockError)

      await deleteApplicationHandler(req, res, next)

      expect(failureResponse).toHaveBeenCalledWith(
        res, 
        next, 
        'generic_server_error', 
        { error: mockError }
      )
    })

    test('should handle transfer account operations error', async () => {
      getApplication.mockResolvedValue({ ApiKey: 'test-api-key' })
      deleteApplication.mockResolvedValue()
      deleteAllMockMerchantProfileByApplicationUuidAPI.mockResolvedValue()
      updateOneDeletedApplicationInfoByUuid.mockResolvedValue()
      updateOneDeletedApplicationUserByAppId.mockResolvedValue()
      getOneMerchantAPI.mockResolvedValue([])
      getCustomerProfilesByAppIdAPI.mockResolvedValue({ profiles: [] })
      deleteConsentHTMLSQSPayload.mockResolvedValue({ mockPayload: true })
      writeSQS.mockResolvedValue()
      
      const mockError = new Error('Transfer account error')
      getAccountByApplicationUuid.mockRejectedValue(mockError)

      await deleteApplicationHandler(req, res, next)

      expect(failureResponse).toHaveBeenCalledWith(
        res, 
        next, 
        'generic_server_error', 
        { error: mockError }
      )
    })

    test('should call missingParamsResponse if application_uuid missing in params', async () => {
      const req = {
        params: {},
        headers: {
          authorization: 'Bearer mock-token',
          useruuid: 'mock-user-uuid',
        },
      };
      const res = {};
      const next = jest.fn();

      await deleteApplicationHandler(req, res, next);

      expect(missingParamsResponse).toHaveBeenCalledWith(
        res,
        next,
        'missing_param',
        { message: 'application_uuid is required in query parameters' }
      );
    });
  })
})