const chai = require("chai");
const chaiHttp = require("chai-http");
const { afterEach } = require("mocha");
const { expect } = chai;
const sinon = require("sinon");
const sandbox = sinon.createSandbox();

chai.use(chaiHttp);
chai.use(require("chai-json-schema"));

//Test mudule
const apimTestModule = require("../src/model/apim/test");
const merchantsTestModule = require("../src/model/merchants/test/merchant-api");
const appInfoDbTestModule = require("../src/model/applications/test/applicationInfo-db");
const appUsersDbTestModule = require("../src/model/applications/test/applicationUsers-db");
const uploadsApiTestModule = require("../src/model/uploads/test/uploads-api");

//Stub function
const apimGetAppStub = sandbox.stub(apimTestModule, "getApplication");
const getOneMerchantAPIStub = sandbox.stub(
  merchantsTestModule,
  "getOneMerchantAPI"
);
const createOneApplicationRecordStub = sandbox.stub(
  appInfoDbTestModule,
  "createOneApplicationRecord"
);
const lookupApplicationUuidByStatusStub = sandbox.stub(
  appInfoDbTestModule,
  "lookupApplicationUuidByStatus"
);
const addOneApplicationUsersStub = sandbox.stub(
  appUsersDbTestModule,
  "addOneApplicationUsers"
);
const getAppIconURLStub = sandbox.stub(uploadsApiTestModule, "getAppIconURL");

const mockResultGetApp = {
  __metadata: {
    uri: "http://developer-sit.se.scb.co.th:8080/portal-data/Portal.svc/Applications('deb57821-3a59-4d51-93fd-8772f840af05')",
  },
  OrganizationName: "Testaccount.Customportal_48408bce",
  Status: "ENABLED",
  DisabledByType: null,
  Description: "Test hello world",
  ApiKey: "l7e4310bd18db942649b3f025bb4314da2",
  MagMasterKey: "1bd2ede7-2a20-44d3-932e-ecfe3666fa61",
  OauthType: "confidential",
  Reason: null,
  Name: "Application 002 update",
  OrganizationUuid: "48408bce-2574-4608-a985-2ee9689913d5",
  OauthScope:
    "login profiles-sb deeplink-sb transactions-sb transactions.retrieval transactions.cancel profiles.cid profiles.name profiles.birthdate profiles.gender profiles.address profiles.mobile profiles.email profiles.country payment-sb loanorigination-sb profiles.cardtype maemanee.merchant.profile-sb maemanee.payment.qr.create-sb maemanee.payment.paymentlink-sb maemanee.payment.transaction.getlist-sb maemanee.payment.transaction.getone-sb",
  Uuid: "86bc265c-a858-45e5-8e2b-17b1b4a59567",
  KeySecret: "e5963b9e7a444daa8266e6a4aa35972d",
  OauthCallbackUrl: "https://httpbin.org/anything",
  MagScope: "",
  ApiIds: {
    results: [],
  },
  CustomFieldValues: {
    results: [
      {
        Status: "ENABLED",
        Options: "[]",
        TenantId: "developer-test",
        Type: "TEXT",
        Uuid: "deb57821-3a59-4d51-93fd-8772f840af05",
        EntityUuid: "86bc265c-a858-45e5-8e2b-17b1b4a59567",
        Required: false,
        Value:
          "portal-application-icon/266b4deb-d2b8-49eb-8675-02e9daeec380.png",
        CustomFieldUuid: "a0cfbdf7-e07f-43f0-9c92-849451f9d2c3",
        Name: "ApplicationIcon",
      },
    ],
  },
  ApiGroupIds: {
    results: ["b6027d0e-7a52-44de-b6fe-970e87656e88"],
  },
};

const mockResultGetMerchant = [
  {
    merchantId: "017449400898446",
    merchantName: "helloworld",
    billerId: "885284472183810",
    billerName: "world hello",
    notifications: [
      {
        YJQ: "http://helloworld.domain",
        default: true,
      },
      {
        AEQ: "http://callbackurl.com",
        default: false,
      },
    ],
    portal_applicationId: "d92f08d2-a208-4950-a0d5-78db943c6b49",
    referenceType: "2",
    created_at: 1550443371,
    updated_at: 1550443371,
    merchantPans: [
      {
        uuid: "2a6a9ed8-3748-4fad-9257-704472508ec9",
        merchantId: "017449400898446",
        merchantPan: "****************",
        cardCode: "VSA",
        created_at: 1550443371,
        updated_at: 1550443371,
      },
    ],
  },
];

const mockResultGetAppIconURL = {
  uuid: "********-11d3-4e2d-ab61-7df871589bdf",
  url: "https://partner-eco-dev.s3.ap-southeast-1.amazonaws.com/portal-application-icon/87dbf5d6-72f7-4cd4-b10f-afa86ddd1602.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIAJSCILEKJMD7UY52A%2F20190220%2Fap-southeast-1%2Fs3%2Faws4_request&X-Amz-Date=20190220T082132Z&X-Amz-Expires=3600&X-Amz-Signature=9fe1985aab4184e6ff54f1c74683310eb214abe487c834df8cef40f143ef6307&X-Amz-SignedHeaders=host",
};

describe("GET partners/v1/portal/applications/:application_uuid", () => {
    let mochaTestPath;
    let server;
  
    before(() => {
      server = require("../index");
    });
  
    beforeEach(() => {
      sandbox.reset();
    });
  
    afterEach(() => {
      sandbox.restore();
    });
  
    after(() => {
      sandbox.restore();
    });
  
    it("should return 404, Code '6130', Invalid application, When invalid uuid application", (done) => {
      mochaTestPath =
        "/v1/portal/applications/27c682a9-8fac-4fe8-91bd-f0b913a77a9f";
  
      apimGetAppStub.rejects({
        type: "invalid_application",
        details: {
          source: "getApplication()",
          status: 404,
          statusText: "Not Found",
          details: {
            errorCode: "505",
            devErrorMessage: "27c682a9-8fac-4fe8-91bd-f0b913a77a9f not found",
            userErrorMessage: "27c682a9-8fac-4fe8-91bd-f0b913a77a9f not found.",
            userErrorKey: "error.resource.not.found",
          },
        },
      });
  
      chai
        .request(server)
        .get(`${mochaTestPath}`)
        .end((err, res) => {
          expect(res).has.status(404);
          expect(res.body).to.deep.eq({
            status: {
              code: 6130,
              description: "Invalid application",
              details: {
                error: {
                  type: "invalid_application",
                  details: {
                    source: "getApplication()",
                    status: 404,
                    statusText: "Not Found",
                    details: {
                      errorCode: "505",
                      devErrorMessage:
                        "27c682a9-8fac-4fe8-91bd-f0b913a77a9f not found",
                      userErrorMessage:
                        "27c682a9-8fac-4fe8-91bd-f0b913a77a9f not found.",
                      userErrorKey: "error.resource.not.found",
                    },
                  },
                },
              },
            },
          });
          done();
        });
    });
  
    it("should return 500, code'1999', Generic server error, when invalid getApplication", (done) => {
      mochaTestPath =
        "/v1/portal/applications/deb57821-3a59-4d51-93fd-8772f840af05";
  
      apimGetAppStub.rejects({});
  
      chai
        .request(server)
        .get(mochaTestPath)
        .end((err, res) => {
          expect(res).has.status(500);
          expect(res.body).to.deep.eq({
            status: {
              code: 1999,
              description: "Generic server side error",
              details: {
                error: {},
              },
            },
          });
          done();
        });
    });
    
    it("should return 200, Code '1000', Success, When lookupApplicationResponse doesn't exist and merchants exist", (done) => {
      mochaTestPath =
        "/v1/portal/applications/27c682a9-8fac-4fe8-91bd-f0b913a77a9f";
  
      apimGetAppStub.resolves(mockResultGetApp);
      lookupApplicationUuidByStatusStub.resolves([]);
      getOneMerchantAPIStub.resolves(mockResultGetMerchant);
      getAppIconURLStub.resolves(mockResultGetAppIconURL);
  
      chai
        .request(server)
        .get(`${mochaTestPath}`)
        .set("useruuid", "testuseruuid")
        .end((err, res) => {
          expect(res).has.status(200);
          expect(res.body).to.deep.eq({
            status: {
              code: 1000,
              description: "Success",
            },
            data: {
              application: {
                applicationUuid: "86bc265c-a858-45e5-8e2b-17b1b4a59567",
                applicationName: "Application 002 update",
                description: "Test hello world",
                apiKey: "l7e4310bd18db942649b3f025bb4314da2",
                keySecret: "e5963b9e7a444daa8266e6a4aa35972d",
                callbackUrl: "https://httpbin.org/anything",
                organizationUuid: "48408bce-2574-4608-a985-2ee9689913d5",
                organizationName: "Testaccount.Customportal_48408bce",
                apiGroupIds: {
                  results: ["b6027d0e-7a52-44de-b6fe-970e87656e88"],
                },
                customFieldValues: {
                  results: [
                    {
                      Status: "ENABLED",
                      Options: "[]",
                      TenantId: "developer-test",
                      Type: "TEXT",
                      Uuid: "deb57821-3a59-4d51-93fd-8772f840af05",
                      EntityUuid: "86bc265c-a858-45e5-8e2b-17b1b4a59567",
                      Required: false,
                      Value:
                        "portal-application-icon/266b4deb-d2b8-49eb-8675-02e9daeec380.png",
                      CustomFieldUuid: "a0cfbdf7-e07f-43f0-9c92-849451f9d2c3",
                      Name: "ApplicationIcon",
                    },
                  ],
                },
                status: "ENABLED",
                applicationIcon: {
                  uuid: "********-11d3-4e2d-ab61-7df871589bdf",
                  url: "https://partner-eco-dev.s3.ap-southeast-1.amazonaws.com/portal-application-icon/87dbf5d6-72f7-4cd4-b10f-afa86ddd1602.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIAJSCILEKJMD7UY52A%2F20190220%2Fap-southeast-1%2Fs3%2Faws4_request&X-Amz-Date=20190220T082132Z&X-Amz-Expires=3600&X-Amz-Signature=9fe1985aab4184e6ff54f1c74683310eb214abe487c834df8cef40f143ef6307&X-Amz-SignedHeaders=host",
                },
              },
              merchants: [
                {
                  merchantId: "017449400898446",
                  merchantName: "helloworld",
                  billerId: "885284472183810",
                  billerName: "world hello",
                  notifications: [
                    {
                      YJQ: "http://helloworld.domain",
                      default: true,
                    },
                    {
                      AEQ: "http://callbackurl.com",
                      default: false,
                    },
                  ],
                  portal_applicationId: "d92f08d2-a208-4950-a0d5-78db943c6b49",
                  referenceType: "2",
                  created_at: 1550443371,
                  updated_at: 1550443371,
                  merchantPans: [
                    {
                      uuid: "2a6a9ed8-3748-4fad-9257-704472508ec9",
                      merchantId: "017449400898446",
                      merchantPan: "****************",
                      cardCode: "VSA",
                      created_at: 1550443371,
                      updated_at: 1550443371,
                    },
                  ],
                },
              ],
            },
          });
          done();
        });
    });
  
    it("should return 200, Code '1000', Success, When lookupApplicationResponse exist and merchants exist", (done) => {
      mochaTestPath =
        "/v1/portal/applications/27c682a9-8fac-4fe8-91bd-f0b913a77a9f";
  
      apimGetAppStub.resolves(mockResultGetApp);
      lookupApplicationUuidByStatusStub.resolves(["ithavesomething"]);
      createOneApplicationRecordStub.resolves();
      addOneApplicationUsersStub.resolves();
      getOneMerchantAPIStub.resolves(mockResultGetMerchant);
      getAppIconURLStub.resolves(mockResultGetAppIconURL);
  
      chai
        .request(server)
        .get(`${mochaTestPath}`)
        .set("useruuid", "testuseruuid")
        .end((err, res) => {
          expect(res).has.status(200);
          expect(res.body).to.deep.eq({
            status: {
              code: 1000,
              description: "Success",
            },
            data: {
              application: {
                applicationUuid: "86bc265c-a858-45e5-8e2b-17b1b4a59567",
                applicationName: "Application 002 update",
                description: "Test hello world",
                apiKey: "l7e4310bd18db942649b3f025bb4314da2",
                keySecret: "e5963b9e7a444daa8266e6a4aa35972d",
                callbackUrl: "https://httpbin.org/anything",
                organizationUuid: "48408bce-2574-4608-a985-2ee9689913d5",
                organizationName: "Testaccount.Customportal_48408bce",
                apiGroupIds: {
                  results: ["b6027d0e-7a52-44de-b6fe-970e87656e88"],
                },
                customFieldValues: {
                  results: [
                    {
                      Status: "ENABLED",
                      Options: "[]",
                      TenantId: "developer-test",
                      Type: "TEXT",
                      Uuid: "deb57821-3a59-4d51-93fd-8772f840af05",
                      EntityUuid: "86bc265c-a858-45e5-8e2b-17b1b4a59567",
                      Required: false,
                      Value:
                        "portal-application-icon/266b4deb-d2b8-49eb-8675-02e9daeec380.png",
                      CustomFieldUuid: "a0cfbdf7-e07f-43f0-9c92-849451f9d2c3",
                      Name: "ApplicationIcon",
                    },
                  ],
                },
                status: "ENABLED",
                applicationIcon: {
                  uuid: "********-11d3-4e2d-ab61-7df871589bdf",
                  url: "https://partner-eco-dev.s3.ap-southeast-1.amazonaws.com/portal-application-icon/87dbf5d6-72f7-4cd4-b10f-afa86ddd1602.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIAJSCILEKJMD7UY52A%2F20190220%2Fap-southeast-1%2Fs3%2Faws4_request&X-Amz-Date=20190220T082132Z&X-Amz-Expires=3600&X-Amz-Signature=9fe1985aab4184e6ff54f1c74683310eb214abe487c834df8cef40f143ef6307&X-Amz-SignedHeaders=host",
                },
              },
              merchants: [
                {
                  merchantId: "017449400898446",
                  merchantName: "helloworld",
                  billerId: "885284472183810",
                  billerName: "world hello",
                  notifications: [
                    {
                      YJQ: "http://helloworld.domain",
                      default: true,
                    },
                    {
                      AEQ: "http://callbackurl.com",
                      default: false,
                    },
                  ],
                  portal_applicationId: "d92f08d2-a208-4950-a0d5-78db943c6b49",
                  referenceType: "2",
                  created_at: 1550443371,
                  updated_at: 1550443371,
                  merchantPans: [
                    {
                      uuid: "2a6a9ed8-3748-4fad-9257-704472508ec9",
                      merchantId: "017449400898446",
                      merchantPan: "****************",
                      cardCode: "VSA",
                      created_at: 1550443371,
                      updated_at: 1550443371,
                    },
                  ],
                },
              ],
            },
          });
          done();
        });
    });
  
    it("should return 200, Code '1000', Success, When lookupApplicationResponse exist and merchants doesn't exist", (done) => {
      mochaTestPath =
        "/v1/portal/applications/27c682a9-8fac-4fe8-91bd-f0b913a77a9f";
  
      apimGetAppStub.resolves(mockResultGetApp);
      lookupApplicationUuidByStatusStub.resolves(["ithavesomething"]);
      createOneApplicationRecordStub.resolves();
      addOneApplicationUsersStub.resolves();
      getOneMerchantAPIStub.resolves([]);
      getAppIconURLStub.resolves(mockResultGetAppIconURL);
  
      chai
        .request(server)
        .get(`${mochaTestPath}`)
        .set("useruuid", "testuseruuid")
        .end((err, res) => {
          expect(res).has.status(200);
          expect(res.body).to.deep.eq({
            status: {
              code: 1000,
              description: "Success",
            },
            data: {
              application: {
                applicationUuid: "86bc265c-a858-45e5-8e2b-17b1b4a59567",
                applicationName: "Application 002 update",
                description: "Test hello world",
                apiKey: "l7e4310bd18db942649b3f025bb4314da2",
                keySecret: "e5963b9e7a444daa8266e6a4aa35972d",
                callbackUrl: "https://httpbin.org/anything",
                organizationUuid: "48408bce-2574-4608-a985-2ee9689913d5",
                organizationName: "Testaccount.Customportal_48408bce",
                apiGroupIds: {
                  results: ["b6027d0e-7a52-44de-b6fe-970e87656e88"],
                },
                customFieldValues: {
                  results: [
                    {
                      Status: "ENABLED",
                      Options: "[]",
                      TenantId: "developer-test",
                      Type: "TEXT",
                      Uuid: "deb57821-3a59-4d51-93fd-8772f840af05",
                      EntityUuid: "86bc265c-a858-45e5-8e2b-17b1b4a59567",
                      Required: false,
                      Value:
                        "portal-application-icon/266b4deb-d2b8-49eb-8675-02e9daeec380.png",
                      CustomFieldUuid: "a0cfbdf7-e07f-43f0-9c92-849451f9d2c3",
                      Name: "ApplicationIcon",
                    },
                  ],
                },
                status: "ENABLED",
                applicationIcon: {
                  uuid: "********-11d3-4e2d-ab61-7df871589bdf",
                  url: "https://partner-eco-dev.s3.ap-southeast-1.amazonaws.com/portal-application-icon/87dbf5d6-72f7-4cd4-b10f-afa86ddd1602.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIAJSCILEKJMD7UY52A%2F20190220%2Fap-southeast-1%2Fs3%2Faws4_request&X-Amz-Date=20190220T082132Z&X-Amz-Expires=3600&X-Amz-Signature=9fe1985aab4184e6ff54f1c74683310eb214abe487c834df8cef40f143ef6307&X-Amz-SignedHeaders=host",
                },
              },
              merchants: [],
              merchantLegos:{}
            },
          });
          done();
        });
    });
  
    it("should return 200, Code '1000', Success, When lookupApplicationResponse doesn't exist and merchants doesn't exist", (done) => {
      mochaTestPath =
        "/v1/portal/applications/27c682a9-8fac-4fe8-91bd-f0b913a77a9f";
  
      apimGetAppStub.resolves(mockResultGetApp);
      lookupApplicationUuidByStatusStub.resolves([]);
      getOneMerchantAPIStub.resolves([]);
      getAppIconURLStub.resolves(mockResultGetAppIconURL);
  
      chai
        .request(server)
        .get(`${mochaTestPath}`)
        .set("useruuid", "testuseruuid")
        .end((err, res) => {
          expect(res).has.status(200);
          expect(res.body).to.deep.eq({
            status: {
              code: 1000,
              description: "Success",
            },
            data: {
              application: {
                applicationUuid: "86bc265c-a858-45e5-8e2b-17b1b4a59567",
                applicationName: "Application 002 update",
                description: "Test hello world",
                apiKey: "l7e4310bd18db942649b3f025bb4314da2",
                keySecret: "e5963b9e7a444daa8266e6a4aa35972d",
                callbackUrl: "https://httpbin.org/anything",
                organizationUuid: "48408bce-2574-4608-a985-2ee9689913d5",
                organizationName: "Testaccount.Customportal_48408bce",
                apiGroupIds: {
                  results: ["b6027d0e-7a52-44de-b6fe-970e87656e88"],
                },
                customFieldValues: {
                  results: [
                    {
                      Status: "ENABLED",
                      Options: "[]",
                      TenantId: "developer-test",
                      Type: "TEXT",
                      Uuid: "deb57821-3a59-4d51-93fd-8772f840af05",
                      EntityUuid: "86bc265c-a858-45e5-8e2b-17b1b4a59567",
                      Required: false,
                      Value:
                        "portal-application-icon/266b4deb-d2b8-49eb-8675-02e9daeec380.png",
                      CustomFieldUuid: "a0cfbdf7-e07f-43f0-9c92-849451f9d2c3",
                      Name: "ApplicationIcon",
                    },
                  ],
                },
                status: "ENABLED",
                applicationIcon: {
                  uuid: "********-11d3-4e2d-ab61-7df871589bdf",
                  url: "https://partner-eco-dev.s3.ap-southeast-1.amazonaws.com/portal-application-icon/87dbf5d6-72f7-4cd4-b10f-afa86ddd1602.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIAJSCILEKJMD7UY52A%2F20190220%2Fap-southeast-1%2Fs3%2Faws4_request&X-Amz-Date=20190220T082132Z&X-Amz-Expires=3600&X-Amz-Signature=9fe1985aab4184e6ff54f1c74683310eb214abe487c834df8cef40f143ef6307&X-Amz-SignedHeaders=host",
                },
              },
              merchants: [],
              merchantLegos: {},
            },
          });
          done();
        });
    });
  });
  