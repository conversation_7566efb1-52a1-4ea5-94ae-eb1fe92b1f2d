const chai = require("chai");
const chaiHttp = require("chai-http");
const { afterEach } = require("mocha");
const { expect } = chai;
const sinon = require("sinon");
const sandbox = sinon.createSandbox();

chai.use(chaiHttp);
chai.use(require("chai-json-schema"));

//Test mudule
const apimTestModule = require("../src/model/apim/test");
const merchantsTestModule = require("../src/model/merchants/test/merchant-api");
const appInfoDbTestModule = require("../src/model/applications/test/applicationInfo-db");
const sqsTest = require("../src/util/test/sqs");

//Stub function
const apimGetAppStub = sandbox.stub(apimTestModule, "getApplication");
const apimUpdateAppStub = sandbox.stub(apimTestModule, "updateApplication");
const apimCustomFieldSearch = sandbox.stub(
  apimTestModule,
  "getCustomFieldsByName"
);
const getOneMerchantAPIStub = sandbox.stub(
  merchantsTestModule,
  "getOneMerchantAPI"
);
const updateOneMerchantAPIStub = sandbox.stub(
  merchantsTestModule,
  "updateOneMerchantAPI"
);
const createMerchantAPIStub = sandbox.stub(
  merchantsTestModule,
  "createMerchantAPI"
);
const updateOneApplicationInfoByUuidStub = sandbox.stub(
  appInfoDbTestModule,
  "updateOneApplicationInfoByUuid"
);
const writeSQSStub = sandbox.stub(sqsTest, "writeSQS");

const mockResultGetApp = {
  __metadata: {
    uri: "http://developer-sit.se.scb.co.th:8080/portal-data/Portal.svc/Applications('deb57821-3a59-4d51-93fd-8772f840af05')",
  },
  OrganizationName: "Testaccount.Customportal_48408bce",
  Status: "ENABLED",
  DisabledByType: null,
  Description: "Test hello world",
  ApiKey: "l7e4310bd18db942649b3f025bb4314da2",
  MagMasterKey: "1bd2ede7-2a20-44d3-932e-ecfe3666fa61",
  OauthType: "confidential",
  Reason: null,
  Name: "Application 002 update",
  OrganizationUuid: "48408bce-2574-4608-a985-2ee9689913d5",
  OauthScope:
    "login profiles-sb deeplink-sb transactions-sb transactions.retrieval transactions.cancel profiles.cid profiles.name profiles.birthdate profiles.gender profiles.address profiles.mobile profiles.email profiles.country payment-sb loanorigination-sb profiles.cardtype maemanee.merchant.profile-sb maemanee.payment.qr.create-sb maemanee.payment.paymentlink-sb maemanee.payment.transaction.getlist-sb maemanee.payment.transaction.getone-sb",
  Uuid: "86bc265c-a858-45e5-8e2b-17b1b4a59567",
  KeySecret: "e5963b9e7a444daa8266e6a4aa35972d",
  OauthCallbackUrl: "https://httpbin.org/anything",
  MagScope: "",
  ApiIds: {
    results: [],
  },
  CustomFieldValues: {
    results: [
      {
        Status: "ENABLED",
        Options: "[]",
        TenantId: "developer-test",
        Type: "TEXT",
        Uuid: "deb57821-3a59-4d51-93fd-8772f840af05",
        EntityUuid: "86bc265c-a858-45e5-8e2b-17b1b4a59567",
        Required: false,
        Value:
          "portal-application-icon/266b4deb-d2b8-49eb-8675-02e9daeec380.png",
        CustomFieldUuid: "a0cfbdf7-e07f-43f0-9c92-849451f9d2c3",
        Name: "ApplicationIcon",
      },
    ],
  },
  ApiGroupIds: {
    results: ["b6027d0e-7a52-44de-b6fe-970e87656e88"],
  },
};

const mockResultCustomFieldSearch = [
  {
    __metadata: {
      uri: "http://developer-sit.se.scb.co.th:8080/portal-data/Portal.svc/CustomFields('a0cfbdf7-e07f-43f0-9c92-849451f9d2c3')",
    },
    Status: "ENABLED",
    Description: "",
    Name: "ApplicationIcon",
    EntityType: "APPLICATION",
    Type: "TEXT",
    Uuid: "a0cfbdf7-e07f-43f0-9c92-849451f9d2c3",
    Required: false,
    OptionsList: {
      results: [],
    },
  },
];

const mockResultGetMerchant = [
  {
    merchantId: "017449400898446",
    merchantName: "helloworld",
    billerId: "885284472183810",
    billerName: "world hello",
    notifications: [
      {
        YJQ: "http://helloworld.domain",
        default: true,
      },
      {
        AEQ: "http://callbackurl.com",
        default: false,
      },
    ],
    portal_applicationId: "d92f08d2-a208-4950-a0d5-78db943c6b49",
    referenceType: "2",
    created_at: 1550443371,
    updated_at: 1550443371,
    merchantPans: [
      {
        uuid: "2a6a9ed8-3748-4fad-9257-704472508ec9",
        merchantId: "017449400898446",
        merchantPan: "****************",
        cardCode: "VSA",
        created_at: 1550443371,
        updated_at: 1550443371,
      },
    ],
  },
];

describe("PUT /partners/v1/portal/applications/:application_uuid", () => {
    let mochaTestPath;
    let server;
  
    before(() => {
      server = require("../index");
    });
  
    afterEach(() => {
      sandbox.restore();
    });
  
    after(() => {
      sandbox.restore();
    });
  
    it("should return 400, Code '1999', Missing required parameters, When uuid application is null", (done) => {
      mochaTestPath = "/v1/portal/applications/";
      chai
        .request(server)
        .put(`${mochaTestPath}`)
        .end((err, res) => {
          expect(res).has.status(400);
          expect(res.body).to.deep.eq({
            status: {
              code: 1999,
              description: "Missing required parameters",
            },
            data: {
              message: "applicationUuid is undefined",
            },
          });
          done();
        });
    });
  
    it("should return 400, Code '1999', Invalid parameters entered, When applicationName contains double space or start/end with space", (done) => {
      mochaTestPath =
        "/v1/portal/applications/de5d2799-6fe1-4bf8-8234-e7956d3e6fa9";
  
      const mockBody = {
        application: {
          applicationName: " Application  002  update ",
          description: "Test hello world",
          callbackUrl: "https://httpbin.org/anything",
        },
      };
  
      chai
        .request(server)
        .put(`${mochaTestPath}`)
        .send(mockBody)
        .end((err, res) => {
          expect(res).has.status(400);
          expect(res.body).to.deep.eq({
            status: {
              code: 1999,
              description: "Invalid parameters entered",
            },
            data: {
              message:
                "applicationName should not contains double spaces and not allow to start/end with space",
            },
          });
          done();
        });
    });
  
    it("should return 400, Code '1999', Invalid parameters entered, When applicationName is empty", (done) => {
      mochaTestPath =
        "/v1/portal/applications/de5d2799-6fe1-4bf8-8234-e7956d3e6fa9";
  
      const mockBody = {
        application: {
          applicationName: "",
          description: "Test hello world",
          callbackUrl: "https://httpbin.org/anything",
        },
      };
  
      chai
        .request(server)
        .put(`${mochaTestPath}`)
        .send(mockBody)
        .end((err, res) => {
          expect(res).has.status(400);
          expect(res.body).to.deep.eq({
            status: {
              code: 1999,
              description: "Invalid parameters entered",
            },
            data: {
              message:
                "The 'application.applicationName' field must not be empty!",
            },
          });
          done();
        });
    });
  
    it("should return 400, Code '1999', Invalid parameters entered, When payload required fields in application are missing", (done) => {
      mochaTestPath =
        "/v1/portal/applications/de5d2799-6fe1-4bf8-8234-e7956d3e6fa9";
  
      const mockBody = {
        application: {
          applicationName: "Application 002 update",
          callbackUrl: "https://httpbin.org/anything",
        },
      };
  
      chai
        .request(server)
        .put(`${mochaTestPath}`)
        .send(mockBody)
        .end((err, res) => {
          expect(res).has.status(400);
          expect(res.body).to.deep.eq({
            status: {
              code: 1999,
              description: "Missing required parameters",
            },
            data: {
              message: "The 'application.description' field is required!",
            },
          });
          done();
        });
    });
  
    it("should return 404, Code '6130', Invalid application, When invalid uuid application", (done) => {
      mochaTestPath =
        "/v1/portal/applications/de5d2799-6fe1-4bf8-8234-e7956d3e6fa9";
  
      const mockBody = {
        application: {
          applicationName: "Application 002 update",
          description: "Test hello world",
          callbackUrl: "https://httpbin.org/anything",
        },
      };
  
      apimGetAppStub.rejects({
        type: "invalid_application",
        details: {
          source: "getApplication()",
          status: 404,
          statusText: "Not Found",
          details: {
            errorCode: "505",
            devErrorMessage: "de5d2799-6fe1-4bf8-8234-e7956d3e6fa9 not found",
            userErrorMessage: "de5d2799-6fe1-4bf8-8234-e7956d3e6fa9 not found.",
            userErrorKey: "error.resource.not.found",
          },
        },
      });
  
      chai
        .request(server)
        .get(mochaTestPath)
        .send(mockBody)
        .end((err, res) => {
          expect(res).has.status(404);
          expect(res.body).to.deep.eq({
            status: {
              code: 6130,
              description: "Invalid application",
              details: {
                error: {
                  type: "invalid_application",
                  details: {
                    source: "getApplication()",
                    status: 404,
                    statusText: "Not Found",
                    details: {
                      errorCode: "505",
                      devErrorMessage:
                        "de5d2799-6fe1-4bf8-8234-e7956d3e6fa9 not found",
                      userErrorMessage:
                        "de5d2799-6fe1-4bf8-8234-e7956d3e6fa9 not found.",
                      userErrorKey: "error.resource.not.found",
                    },
                  },
                },
              },
            },
          });
          done();
        });
    });
  
    it("should return 400, Code '6127', Application name is not unique, When has duplicate application names with merchant", (done) => {
      mochaTestPath =
        "/v1/portal/applications/deb57821-3a59-4d51-93fd-8772f840af05";
  
      const mockBody = {
        application: {
          applicationName: "Application 002 update",
          description: "Test hello world",
          callbackUrl: "https://httpbin.org/anything",
        },
        merchants: [
          {
            merchantId: "716504879354997",
            merchantName: "CCC",
            billerName: "FFF",
            referenceType: "2",
            notifications: [
              {
                prefix: "DRRL",
                callbackUrl: "https://httpbin.org/anythings",
                default: true,
              },
            ],
            merchantLegoName: "Test lego merchant",
          },
        ],
      };
  
      apimGetAppStub.resolves(mockResultGetApp);
      apimCustomFieldSearch.resolves(mockResultCustomFieldSearch);
      getOneMerchantAPIStub.resolves(mockResultGetMerchant);
  
      apimUpdateAppStub.rejects({
        type: "duplicate_application_name",
        details: {
          errorCode: "483",
          devErrorMessage:
            "The request could not be completed due to data input errors.",
          userErrorMessage:
            "The request could not be completed due to data input errors.",
          userErrorKey: "error.validation.entity",
          validationErrors: [
            {
              field: "Name",
              error: "Application name is not unique.",
              key: "error.application.edit.name.unique",
            },
          ],
        },
      });
  
      chai
        .request(server)
        .put(mochaTestPath)
        .send(mockBody)
        .end((err, res) => {
          expect(res).has.status(400);
          expect(res.body).to.deep.eq({
            status: {
              code: 6127,
              description: "Application name is not unique",
              details: {
                error: {
                  type: "duplicate_application_name",
                  details: {
                    errorCode: "483",
                    devErrorMessage:
                      "The request could not be completed due to data input errors.",
                    userErrorMessage:
                      "The request could not be completed due to data input errors.",
                    userErrorKey: "error.validation.entity",
                    validationErrors: [
                      {
                        field: "Name",
                        error: "Application name is not unique.",
                        key: "error.application.edit.name.unique",
                      },
                    ],
                  },
                },
              },
            },
          });
          done();
        });
    });
  
    it("should return 400, Code '6127', Application name is not unique, When has duplicate application names without merchant", (done) => {
      mochaTestPath =
        "/v1/portal/applications/deb57821-3a59-4d51-93fd-8772f840af05";
  
      const mockBody = {
        application: {
          applicationName: "Application 002 update",
          description: "Test hello world",
          callbackUrl: "https://httpbin.org/anything",
        },
        merchants: [
          {
            merchantId: "716504879354997",
            merchantName: "CCC",
            billerName: "FFF",
            referenceType: "2",
            notifications: [
              {
                prefix: "DRRL",
                callbackUrl: "https://httpbin.org/anythings",
                default: true,
              },
            ],
            merchantLegoName: "Test lego merchant",
          },
        ],
      };
  
      apimGetAppStub.resolves(mockResultGetApp);
      apimCustomFieldSearch.resolves(mockResultCustomFieldSearch);
      getOneMerchantAPIStub.resolves([]);
  
      apimUpdateAppStub.rejects({
        type: "duplicate_application_name",
        details: {
          errorCode: "483",
          devErrorMessage:
            "The request could not be completed due to data input errors.",
          userErrorMessage:
            "The request could not be completed due to data input errors.",
          userErrorKey: "error.validation.entity",
          validationErrors: [
            {
              field: "Name",
              error: "Application name is not unique.",
              key: "error.application.edit.name.unique",
            },
          ],
        },
      });
  
      chai
        .request(server)
        .put(mochaTestPath)
        .send(mockBody)
        .end((err, res) => {
          expect(res).has.status(400);
          expect(res.body).to.deep.eq({
            status: {
              code: 6127,
              description: "Application name is not unique",
              details: {
                error: {
                  type: "duplicate_application_name",
                  details: {
                    errorCode: "483",
                    devErrorMessage:
                      "The request could not be completed due to data input errors.",
                    userErrorMessage:
                      "The request could not be completed due to data input errors.",
                    userErrorKey: "error.validation.entity",
                    validationErrors: [
                      {
                        field: "Name",
                        error: "Application name is not unique.",
                        key: "error.application.edit.name.unique",
                      },
                    ],
                  },
                },
              },
            },
          });
          done();
        });
    });
  
    it("should return 400, Code '1999', Invalid parameters entered, When merchantName is empty", (done) => {
      mochaTestPath =
        "/v1/portal/applications/de5d2799-6fe1-4bf8-8234-e7956d3e6fa9";
  
      const mockBody = {
        application: {
          applicationName: "Application 002 update",
          description: "Test hello world",
          callbackUrl: "https://httpbin.org/anything",
        },
        merchants: [
          {
            merchantId: "716504879354997",
            merchantName: "",
            billerName: "FFF",
            referenceType: "2",
            notifications: [
              {
                prefix: "DRRL",
                callbackUrl: "https://httpbin.org/anythings",
                default: true,
              },
            ],
            merchantLegoName: "Test lego merchant",
          },
        ],
      };
  
      apimGetAppStub.resolves(mockResultGetApp);
      apimCustomFieldSearch.resolves(mockResultCustomFieldSearch);
      getOneMerchantAPIStub.resolves(mockResultGetMerchant);
  
      chai
        .request(server)
        .put(`${mochaTestPath}`)
        .send(mockBody)
        .end((err, res) => {
          expect(res).has.status(400);
          expect(res.body).to.deep.eq({
            status: {
              code: 1999,
              description: "Invalid parameters entered",
            },
            data: {
              message: "The 'merchants[0].merchantName' field must not be empty!",
            },
          });
          done();
        });
    });
  
    it("should return 400, Code '1999', Invalid parameters entered, When payload required fields in merchants are missing", (done) => {
      mochaTestPath =
        "/v1/portal/applications/de5d2799-6fe1-4bf8-8234-e7956d3e6fa9";
  
      const mockBody = {
        application: {
          applicationName: "Application 002 update",
          description: "Test hello world",
          callbackUrl: "https://httpbin.org/anything",
        },
      };
  
      apimGetAppStub.resolves(mockResultGetApp);
      apimCustomFieldSearch.resolves(mockResultCustomFieldSearch);
      getOneMerchantAPIStub.resolves(mockResultGetMerchant);
  
      chai
        .request(server)
        .put(`${mochaTestPath}`)
        .send(mockBody)
        .end((err, res) => {
          expect(res).has.status(400);
          expect(res.body).to.deep.eq({
            status: {
              code: 1999,
              description: "Missing required parameters",
            },
            data: {
              message: "The 'merchants' field is required!",
            },
          });
          done();
        });
    });
  
    it("should return 400, Code '1999', Invalid parameters entered, When payload required fields in merchants are missing", (done) => {
      mochaTestPath =
        "/v1/portal/applications/de5d2799-6fe1-4bf8-8234-e7956d3e6fa9";
  
      const mockBody = {
        application: {
          applicationName: "Application 002 update",
          description: "Test hello world",
          callbackUrl: "https://httpbin.org/anything",
        },
      };
  
      apimGetAppStub.resolves(mockResultGetApp);
      apimCustomFieldSearch.resolves(mockResultCustomFieldSearch);
      getOneMerchantAPIStub.resolves([]);
  
      chai
        .request(server)
        .put(`${mochaTestPath}`)
        .send(mockBody)
        .end((err, res) => {
          expect(res).has.status(400);
          expect(res.body).to.deep.eq({
            status: {
              code: 1999,
              description: "Missing required parameters",
            },
            data: {
              message: "The 'merchants' field is required!",
            },
          });
          done();
        });
    });
  
    it("should return 400, Code '1999', Invalid parameters entered, When merchantName longer than 25 characters", (done) => {
      mochaTestPath =
        "/v1/portal/applications/de5d2799-6fe1-4bf8-8234-e7956d3e6fa9";
  
      const mockBody = {
        application: {
          applicationName: "Application 002 update",
          description: "Test hello world",
          callbackUrl: "https://httpbin.org/anything",
        },
        merchants: [
          {
            merchantId: "716504879354997",
            merchantName: "letmakemerchantsnamelargerthantwentyfivecharacters",
            billerName: "FFF",
            referenceType: "2",
            notifications: [
              {
                prefix: "DRRL",
                callbackUrl: "https://httpbin.org/anythings",
                default: true,
              },
            ],
            merchantLegoName: "Test lego merchant",
          },
        ],
      };
  
      apimGetAppStub.resolves(mockResultGetApp);
      apimCustomFieldSearch.resolves(mockResultCustomFieldSearch);
      getOneMerchantAPIStub.resolves([]);
  
      chai
        .request(server)
        .put(`${mochaTestPath}`)
        .send(mockBody)
        .end((err, res) => {
          expect(res).has.status(400);
          expect(res.body).to.deep.eq({
            status: {
              code: 1999,
              description: "Invalid parameters entered",
            },
            data: {
              message:
                "The 'merchants[0].merchantName' field length must be less than or equal to 25 characters long!",
            },
          });
          done();
        });
    });
  
    it("should return 400, code '6131', Invalid merchant, when invalid merchant", (done) => {
      mochaTestPath =
        "/v1/portal/applications/deb57821-3a59-4d51-93fd-8772f840af05";
  
      const mockBody = {
        application: {
          applicationName: "Application 002 update",
          description: "Test hello world",
          callbackUrl: "https://httpbin.org/anything",
        },
        merchants: [
          {
            merchantId: "716504879354997",
            merchantName: "CCC",
            billerName: "FFF",
            referenceType: "2",
            notifications: [
              {
                prefix: "DRRL",
                callbackUrl: "https://httpbin.org/anythings",
                default: true,
              },
            ],
            merchantLegoName: "Test lego merchant",
          },
        ],
      };
  
      apimGetAppStub.resolves(mockResultGetApp);
      getOneMerchantAPIStub.resolves(mockResultGetMerchant);
      apimUpdateAppStub.resolves();
      updateOneApplicationInfoByUuidStub.resolves();
      updateOneMerchantAPIStub.resolves({
        statusCode: 400,
        body: {
          status: {
            code: 6131,
            description: "Invalid merchant",
            details: {
              type: "invalid_merchant",
            },
          },
        },
      });
      writeSQSStub.resolves();
  
      chai
        .request(server)
        .put(`${mochaTestPath}`)
        .send(mockBody)
        .end((err, res) => {
          expect(res).has.status(400);
          expect(res.body).to.deep.eq({
            status: {
              code: 6131,
              description: "Invalid merchant",
              details: {
                type: "invalid_merchant",
              },
            },
          });
          done();
        });
    });
  
    it("should return 500, code'1999', Generic server error, when invalid getApplication", (done) => {
      mochaTestPath =
        "/v1/portal/applications/deb57821-3a59-4d51-93fd-8772f840af05";
  
      apimGetAppStub.rejects({});
  
      chai
        .request(server)
        .put(mochaTestPath)
        .end((err, res) => {
          expect(res).has.status(500);
          expect(res.body).to.deep.eq({
            status: {
              code: 1999,
              description: "Generic server side error",
              details: {
                error: {},
              },
            },
          });
          done();
        });
    });
  
    it("should return 400, code '6131', Invalid merchant, when throw createMerchantResponse", (done) => {
      mochaTestPath =
        "/v1/portal/applications/deb57821-3a59-4d51-93fd-8772f840af05";
  
      const mockBody = {
        application: {
          applicationName: "Application 002 update",
          description: "Test hello world",
          callbackUrl: "https://httpbin.org/anything",
        },
        merchants: [
          {
            merchantId: "716504879354997",
            merchantName: "CCC",
            billerName: "FFF",
            referenceType: "2",
            notifications: [
              {
                prefix: "DRRL",
                callbackUrl: "https://httpbin.org/anythings",
                default: true,
              },
            ],
            merchantLegoName: "Test lego merchant",
          },
        ],
      };
  
      apimGetAppStub.resolves(mockResultGetApp);
      getOneMerchantAPIStub.resolves([]);
      apimUpdateAppStub.resolves();
      updateOneApplicationInfoByUuidStub.resolves();
      createMerchantAPIStub.resolves({
        statusCode: 400,
        body: {
          status: {
            code: 6131,
            description: "Invalid merchant",
            details: {
              type: "invalid_merchant",
            },
          },
        },
      });
      writeSQSStub.resolves();
  
      chai
        .request(server)
        .put(`${mochaTestPath}`)
        .send(mockBody)
        .end((err, res) => {
          expect(res).has.status(400);
          expect(res.body).to.deep.eq({
            status: {
              code: 6131,
              description: "Invalid merchant",
              details: {
                type: "invalid_merchant",
              },
            },
          });
          done();
        });
    });
  
    it("should return 200, code '1000', Success, with merchants don't exist", (done) => {
      mochaTestPath =
        "/v1/portal/applications/deb57821-3a59-4d51-93fd-8772f840af05";
  
      const mockBody = {
        application: {
          applicationName: "Application 002 update",
          description: "Test hello world",
          callbackUrl: "https://httpbin.org/anything",
        },
        merchants: [
          {
            merchantId: "716504879354997",
            merchantName: "CCC",
            billerName: "FFF",
            referenceType: "2",
            notifications: [
              {
                prefix: "DRRL",
                callbackUrl: "https://httpbin.org/anythings",
                default: true,
              },
            ],
            merchantLegoName: "Test lego merchant",
          },
        ],
      };
  
      apimGetAppStub.resolves(mockResultGetApp);
      getOneMerchantAPIStub.resolves([]);
      apimUpdateAppStub.resolves();
      updateOneApplicationInfoByUuidStub.resolves();
      createMerchantAPIStub.resolves({ statusCode: 200 });
      writeSQSStub.resolves();
  
      chai
        .request(server)
        .put(`${mochaTestPath}`)
        .send(mockBody)
        .end((err, res) => {
          expect(res).has.status(200);
          expect(res.body).to.deep.eq({
            status: {
              code: 1000,
              description: "Success",
            },
          });
          done();
        });
    });
  
    it("should return 200, code '1000', Success, with merchants exist", (done) => {
      mochaTestPath =
        "/v1/portal/applications/deb57821-3a59-4d51-93fd-8772f840af05";
  
      const mockBody = {
        application: {
          applicationName: "Application 002 update",
          description: "Test hello world",
          callbackUrl: "https://httpbin.org/anything",
          applicationIcon: "APPLICATION_ICON",
        },
        merchants: [
          {
            merchantId: "716504879354997",
            merchantName: "CCC",
            billerName: "FFF",
            referenceType: "2",
            notifications: [
              {
                prefix: "DRRL",
                callbackUrl: "https://httpbin.org/anythings",
                default: true,
              },
            ],
            merchantLegoName: "Test lego merchant",
          },
        ],
      };
  
      apimGetAppStub.resolves(mockResultGetApp);
      apimCustomFieldSearch.resolves(mockResultCustomFieldSearch);
      getOneMerchantAPIStub.resolves(mockResultGetMerchant);
      apimUpdateAppStub.resolves();
      updateOneApplicationInfoByUuidStub.resolves();
      updateOneMerchantAPIStub.resolves({ statusCode: 200 });
      writeSQSStub.resolves();
  
      chai
        .request(server)
        .put(`${mochaTestPath}`)
        .send(mockBody)
        .end((err, res) => {
          expect(res).has.status(200);
          expect(res.body).to.deep.eq({
            status: {
              code: 1000,
              description: "Success",
            },
          });
          done();
        });
    });
  });
  